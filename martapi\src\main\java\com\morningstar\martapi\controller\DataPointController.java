package com.morningstar.martapi.controller;

import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.morningstar.martcommon.entity.datapoint.DataPoint;
import com.morningstar.martcommon.entity.datapoint.DataPointRepository;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@RestController
@RequestMapping(value = {"/v1/security-data","/investment-api/v1/security-data"},
        produces = {MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE})
@Slf4j
public class DataPointController {
    private static final ObjectMapper mapper = new ObjectMapper();

    static {
        mapper.setSerializationInclusion(Include.NON_NULL);
    }
    @GetMapping(value = "/datapoints/{idlist}")
    public Mono<String> getDatapointValues(
            @PathVariable("idlist") String idList,
            @RequestHeader(value = "X-Api-ProductId", required = false, defaultValue = "") String productId,
            @RequestHeader(value = "X-API-RequestId", required = false, defaultValue = "") String requestId
    ) {
        List<String> rawList = Arrays.asList(idList.split(","));
        List<DataPoint> dpList = new ArrayList<>();
        rawList.forEach(id -> dpList.add(DataPointRepository.getByNid(id)));
        String json = "";

        try {
            json = mapper.writerWithDefaultPrettyPrinter().writeValueAsString(dpList);
        } catch (IOException e) {
            log.info(String.format("Datapoint controller json parse exception for id: %s", idList));
        }

        return Mono.just(json);
    }

}
