package com.morningstar.martapi.controller;

import static com.morningstar.martapi.config.Constant.LOG_REQUEST_PAYLOAD_LIMIT_MILLISECONDS;
import static org.junit.Assert.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

import com.morningstar.martapi.validator.RequestValidationHandler;
import com.morningstar.martgateway.domains.core.entity.investmentresponse.Investment;
import com.morningstar.martgateway.domains.core.entity.investmentresponse.InvestmentResponse;
import com.morningstar.martgateway.domains.core.entity.response.Status;
import com.morningstar.martgateway.domains.timeseries.entity.TSContent;
import com.morningstar.martgateway.domains.timeseries.entity.TSData;
import com.morningstar.martgateway.domains.timeseries.entity.TSItem;
import com.morningstar.martgateway.domains.timeseries.entity.TSResponse;
import com.morningstar.martgateway.domains.timeseries.entity.TSStatus;
import com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf;
import com.morningstar.martgateway.interfaces.TsNewRspGatewayImpl;
import com.morningstar.martgateway.interfaces.TsOldRspGatewayImpl;
import com.morningstar.martcommon.entity.result.request.MartRequest;
import java.util.Collections;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.mock.http.server.reactive.MockServerHttpRequest;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.List;

public class TimeSeriesControllerTest {

    private TimeSeriesController controller;
    private TsOldRspGatewayImpl tsOldRspGatewayImpl;
    private TsNewRspGatewayImpl tsNewRspGatewayImpl;

    @Before
    public void setup() {
        this.tsOldRspGatewayImpl = Mockito.mock(TsOldRspGatewayImpl.class);
        this.tsNewRspGatewayImpl = Mockito.mock(TsNewRspGatewayImpl.class);
        this.controller = new TimeSeriesController(tsOldRspGatewayImpl, tsNewRspGatewayImpl, new RequestValidationHandler<>(Collections.emptyList(), Collections.emptyList()));
    }

    @Test
    public void retrieveTimeSeriesDataJson() {
        when(tsNewRspGatewayImpl.asyncRetrieveSecurities(ArgumentMatchers.any(MartRequest.class))).thenReturn(Mono.just(buildInvestmentResponse()));
        Mono<InvestmentResponse> response =  controller.getInvestmentTSData("F000010IS3", "HP010", "2000-01-01", "2023-12-31", "", "", "","","", "","","","","","", "", "", "", "", "", "", MockServerHttpRequest.get("/").build());
        response.subscribe();
        Mockito.verify(tsNewRspGatewayImpl, Mockito.times(1)).asyncRetrieveSecurities(ArgumentMatchers.any(MartRequest.class));
        StepVerifier.create(response)
                .expectSubscription()
                .assertNext(r -> {
                    Assert.assertEquals(r.getClass(), InvestmentResponse.class);
                });
    }

    @Test
    public void retrieveTimeSeriesDataJson2() {
        when(tsNewRspGatewayImpl.asyncRetrieveSecurities(ArgumentMatchers.any(MartRequest.class))).thenAnswer(invocation -> {
            Thread.sleep(LOG_REQUEST_PAYLOAD_LIMIT_MILLISECONDS + 100);
            return Mono.just(buildInvestmentResponse());
        });
        Mono<InvestmentResponse> response =  controller.getInvestmentTSData("F000010IS3", "HP010", "2000-01-01", "2023-12-31", "", "", "","","", "","","","","","", "", "", "", "", "", "", MockServerHttpRequest.get("/").build());
        response.subscribe();
        Mockito.verify(tsNewRspGatewayImpl, Mockito.times(1)).asyncRetrieveSecurities(ArgumentMatchers.any(MartRequest.class));
        StepVerifier.create(response)
                .expectSubscription()
                .assertNext(r -> {
                    Assert.assertEquals(r.getClass(), InvestmentResponse.class);
                });
    }

    @Test
    public void retrieveTimeSeriesDataJsonErrorTest() {
        when(tsNewRspGatewayImpl.asyncRetrieveSecurities(ArgumentMatchers.any(MartRequest.class))).thenReturn(Mono.error(new RuntimeException("An error occurred")));
        Mono<InvestmentResponse> response =  controller.getInvestmentTSData("F000010IS3", "HP010", "2000-01-01", "2023-12-31", "", "", "","","", "","","","","","", "", "", "", "", "", "", MockServerHttpRequest.get("/").build());
        Mockito.verify(tsNewRspGatewayImpl, Mockito.times(1)).asyncRetrieveSecurities(ArgumentMatchers.any(MartRequest.class));
        response.subscribe();
    }

    @Test
    public void retrieveTimeSeriesDataJsonEmptyTest() {
        when(tsNewRspGatewayImpl.asyncRetrieveSecurities(ArgumentMatchers.any(MartRequest.class))).thenReturn(Mono.empty());
        Mono<InvestmentResponse> response =  controller.getInvestmentTSData("F000010IS3", "HP010", "2000-01-01", "2023-12-31", "", "", "","","", "","","","","","", "", "", "", "", "", "", MockServerHttpRequest.get("/").build());
        Mockito.verify(tsNewRspGatewayImpl, Mockito.times(1)).asyncRetrieveSecurities(ArgumentMatchers.any(MartRequest.class));
        response.subscribe();
    }

    @Test
    public void retrieveTimeSeriesData() {
        when(tsOldRspGatewayImpl.asyncRetrieveSecurities(ArgumentMatchers.any(MartRequest.class))).thenReturn(Mono.just(buildTsResponse()));
        Mono<TSResponse> response =  controller.retrieveTimeSeriesData("F000010IS3", "HP010", "2000-01-01", "2023-12-31", "", "", "","","","", "","","","", "", "", "", "", "", "", "", MockServerHttpRequest.get("/").build());
        Mockito.verify(tsOldRspGatewayImpl, Mockito.times(1)).asyncRetrieveSecurities(ArgumentMatchers.any(MartRequest.class));
        response.subscribe();
    }

    @Test
    public void retrieveTimeSeriesData2() {
        when(tsOldRspGatewayImpl.asyncRetrieveSecurities(ArgumentMatchers.any(MartRequest.class))).thenAnswer(invocation -> {
            Thread.sleep(LOG_REQUEST_PAYLOAD_LIMIT_MILLISECONDS + 100);
            return Mono.just(buildTsResponse());
        });
        Mono<TSResponse> response =  controller.retrieveTimeSeriesData("F000010IS3", "HP010", "2000-01-01", "2023-12-31", "", "", "","","","", "","","","","", "", "", "", "", "", "",  MockServerHttpRequest.get("/").build());
        Mockito.verify(tsOldRspGatewayImpl, Mockito.times(1)).asyncRetrieveSecurities(ArgumentMatchers.any(MartRequest.class));
        response.subscribe();
    }

    @Test
    public void retrieveTimeSeriesDataErrorTest() {
        when(tsOldRspGatewayImpl.asyncRetrieveSecurities(ArgumentMatchers.any(MartRequest.class))).thenReturn(Mono.error(new RuntimeException("An error occurred")));
        Mono<TSResponse> response =  controller.retrieveTimeSeriesData("F000010IS3", "HP010", "2000-01-01", "2023-12-31", "", "", "","","","", "","","","", "", "", "", "", "", "", "", MockServerHttpRequest.get("/").build());
        Mockito.verify(tsOldRspGatewayImpl, Mockito.times(1)).asyncRetrieveSecurities(ArgumentMatchers.any(MartRequest.class));
        response.subscribe();
    }

    @Test
    public void retrieveTimeSeriesDataAsProtobuf() {
        when(tsOldRspGatewayImpl.asyncRetrieveSecurities(ArgumentMatchers.any(MartRequest.class))).thenReturn(Mono.just(buildTsResponse()));
        Mono<TsCacheDataForProtoBuf.TimeSeriesDatas> response =  controller.retrieveTimeSeriesDataAsProtobuf("F000010IS3", "HP010", "2000-01-01", "2023-12-31", "", "", "", "","","","","","","","", "", "", "", "", "", "", MockServerHttpRequest.get("/").build());
        response.subscribe();
    }

    @Test
    public void retrieveTimeSeriesDataAsProtobuf2() {
        when(tsOldRspGatewayImpl.asyncRetrieveSecurities(ArgumentMatchers.any(MartRequest.class))).thenAnswer(invocation -> {
            Thread.sleep(LOG_REQUEST_PAYLOAD_LIMIT_MILLISECONDS + 100);
            return Mono.just(buildTsResponse());
        });
        Mono<TsCacheDataForProtoBuf.TimeSeriesDatas> response =  controller.retrieveTimeSeriesDataAsProtobuf("F000010IS3", "HP010", "2000-01-01", "2023-12-31", "", "", "", "","","","","","","","", "", "", "", "", "", "", MockServerHttpRequest.get("/").build());
        response.subscribe();
    }

    @Test
    public void retrieveTimeSeriesDataAsProtobufErrorTest() {
        when(tsOldRspGatewayImpl.asyncRetrieveSecurities(ArgumentMatchers.any(MartRequest.class))).thenReturn(Mono.error(new RuntimeException("An error occurred")));
        Mono<TsCacheDataForProtoBuf.TimeSeriesDatas> response =  controller.retrieveTimeSeriesDataAsProtobuf("F000010IS3", "HP010", "2000-01-01", "2023-12-31", "", "", "", "","","","","","","","", "", "", "", "", "", "", MockServerHttpRequest.get("/").build());
        StepVerifier.create(response)
                .expectSubscription()
                .expectError()
                .verify();
    }

    private InvestmentResponse buildInvestmentResponse() {
        Investment investment = new Investment("F000010IS3");
        InvestmentResponse investmentResponse = new InvestmentResponse();
        investmentResponse.setStatus(Status.OK);
        investmentResponse.setInvestments(List.of(investment));
        return investmentResponse;
    }

    private TSResponse buildTsResponse() {
        TSItem item = new TSItem();
        item.setSecid("F000010IS3");
        TSContent content = new TSContent();
        content.setItems(List.of(item));
        TSResponse tsResponse = new TSResponse();
        tsResponse.setStatus(new TSStatus());
        tsResponse.setContent(content);
        return tsResponse;
    }

    @Test
    public void retrieveTimeSeriesDataAsProtobufWithFilteredData() {
        when(tsOldRspGatewayImpl.asyncRetrieveSecurities(ArgumentMatchers.any(MartRequest.class))).thenReturn(Mono.just(buildTsResponseWithFilteredData()));
        Mono<TsCacheDataForProtoBuf.TimeSeriesDatas> response =  controller.retrieveTimeSeriesDataAsProtobuf("F00001IIYM,5PUSA00003", "AU002,HP010", "2023-01-01", "2023-12-31", "", "", "", "","","","","","","","", "", "", "", "true", "", "", MockServerHttpRequest.get("/").build());

        StepVerifier.create(response)
                .expectSubscription()
                .assertNext(r ->{
                    assertEquals(200200, r.getRetcode());
                    assertEquals("F00001IIYM", r.getValues(0).getSecId());
                    assertEquals("FO", r.getValues(0).getUniverse());
                    assertEquals("AU002", r.getValues(0).getDataId());
                    assertEquals(1, r.getValues(0).getValuesList().size());
                    assertEquals(49235, r.getValues(0).getValues(0).getDates(0));
                    assertTrue(100.0 == r.getValues(0).getValues(0).getValues(0));

                    assertEquals("F00001IIYM", r.getValues(1).getSecId());
                    assertEquals("FO", r.getValues(1).getUniverse());
                    assertEquals("HP010", r.getValues(1).getDataId());
                    assertEquals("403", r.getValues(1).getErrorCode());

                    assertEquals("5PUSA00003", r.getValues(2).getSecId());
                    assertEquals("5P", r.getValues(2).getUniverse());
                    assertEquals("HP010", r.getValues(2).getDataId());
                    assertEquals("403", r.getValues(2).getErrorCode());
                })
                .expectComplete()
                .verify();
    }

    private TSResponse buildTsResponseWithFilteredData() {
        TSData tsData = new TSData();
        tsData.setDate(49235);
        tsData.setValue(100.0);
        TSItem item = new TSItem();
        item.setSecid("F00001IIYM;FO");
        item.setDataid("AU002");
        item.setData(List.of(tsData));
        TSItem item2 = new TSItem();
        item2.setSecid("F00001IIYM;FO");
        item2.setDataid("HP010");
        item2.setErrorcode("403");
        TSItem item3 = new TSItem();
        item3.setSecid("5PUSA00003;5P");
        item3.setDataid("HP010");
        item3.setErrorcode("403");
        TSContent content = new TSContent();
        content.setItems(List.of(item, item2, item3));
        TSResponse tsResponse = new TSResponse();
        tsResponse.setStatus(new TSStatus(Status.SUCCESS_WITH_DP_ERRORS.getCode(), Status.SUCCESS_WITH_DP_ERRORS.getMessage()));
        tsResponse.setContent(content);
        return tsResponse;
    }
}
