package com.morningstar.martapi.validator.investmentapi;

import com.morningstar.martapi.exception.InvestmentApiValidationException;
import com.morningstar.martapi.validator.Validator;
import com.morningstar.martgateway.domains.core.entity.response.Status;
import com.morningstar.martcommon.entity.payload.gridview.GridviewDataPoint;
import com.morningstar.martgateway.interfaces.model.investmentapi.InvestmentApiRequest;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

public class DateValidator implements Validator<InvestmentApiRequest> {

    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    @Override
    public void validate(InvestmentApiRequest request) throws RuntimeException {
        for (GridviewDataPoint dataPoint : request.getDataPoints()) {
            if (isWithoutDateRange(dataPoint)) {
                continue;
            }
            if (isMissingDate(dataPoint) || isInvalidDateOrder(dataPoint)) {
                throw new InvestmentApiValidationException(Status.INVALID_DATE_SETTING);
            }
        }
    }

    private boolean isInvalidDateOrder(GridviewDataPoint dataPoint) {
        try {
            LocalDate startDate = LocalDate.parse(dataPoint.getStartDate(), formatter);
            LocalDate endDate = LocalDate.parse(dataPoint.getEndDate(), formatter);
            return startDate.isAfter(endDate) || !dataPoint.getStartDate().equals(startDate.format(formatter)) || !dataPoint.getEndDate().equals(endDate.format(formatter));
        } catch (DateTimeParseException e) {
            return true;
        }
    }

    private boolean isMissingDate(GridviewDataPoint dataPoint) {
        String startDate = dataPoint.getStartDate();
        String endDate = dataPoint.getEndDate();
        return (StringUtils.isEmpty(startDate) && StringUtils.isNotEmpty(endDate)) ||
               (StringUtils.isNotEmpty(startDate) && StringUtils.isEmpty(endDate));
    }

    private boolean isWithoutDateRange(GridviewDataPoint dataPoint) {
        return StringUtils.isEmpty(dataPoint.getStartDate()) && StringUtils.isEmpty(dataPoint.getEndDate());
    }
}
