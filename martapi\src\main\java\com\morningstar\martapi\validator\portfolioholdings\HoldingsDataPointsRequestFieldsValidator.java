package com.morningstar.martapi.validator.portfolioholdings;

import com.morningstar.dataac.martgateway.data.ph.entity.HoldingDataPoint;
import com.morningstar.dataac.martgateway.data.ph.entity.HoldingDataRequest;
import com.morningstar.dataac.martgateway.data.ph.service.PhDataPointInfo;
import com.morningstar.martapi.exception.HoldingValidationException;
import com.morningstar.martapi.validator.Validator;
import com.morningstar.martcommon.entity.datapoint.DataPointRepository;
import com.morningstar.martcommon.utils.DataPointConstants;
import com.morningstar.martgateway.domains.core.entity.response.Status;
import org.apache.commons.lang3.StringUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class HoldingsDataPointsRequestFieldsValidator implements Validator<HoldingDataRequest> {

    @Override
    public void validate(HoldingDataRequest request) throws RuntimeException {
        List<HoldingDataPoint> dataPoints = request.getPortfolioSetting().getHoldingsDataPoints();
        validateDataPointsNotEmpty(dataPoints);

        Set<String> distinctAliases = new HashSet<>();
        for (HoldingDataPoint dataPoint : dataPoints) {
            validateDataPointId(dataPoint);
            validateAliasRules(dataPoint, distinctAliases);
            validateCurrency(dataPoint);
            validateDataPointHasValidSource(dataPoint);
        }
    }

    private void validateDataPointHasValidSource(HoldingDataPoint dataPoint) {
        if (hasInvalidSource(dataPoint)) {
            throw new HoldingValidationException(
                    Status.BAD_REQUEST.withMessage("Data point ID '" + dataPoint.getDataPointId() + "' is not a portfolio holding data point"));
        }
    }

    private static boolean hasInvalidSource(HoldingDataPoint dataPoint) {
        return !PhDataPointInfo.isPhDataPoint(dataPoint.getDataPointId()) &&
                !isIceDataPoint(dataPoint.getDataPointId());
    }

    private static boolean isIceDataPoint(String dataPointId) {
        String source = DataPointRepository.getByNid(dataPointId).getSrc();
        if (source == null) {
            return false;
        }
        String[] sources = source.split(",");
        for (String src : sources) {
            if (DataPointConstants.ICEFI.equals(src.trim())) {
                return true;
            }
        }
        return false;
    }

    private void validateDataPointsNotEmpty(List<HoldingDataPoint> dataPoints) {
        if (dataPoints == null || dataPoints.isEmpty()) {
            throw new HoldingValidationException(
                    Status.MISSING_ATTRIBUTE.withMessage("Mandatory field `holdingsDataPoints` is missing or empty")
            );
        }
    }

    private void validateDataPointId(HoldingDataPoint dataPoint) {
        if (StringUtils.isEmpty(dataPoint.getDataPointId())) {
            throw new HoldingValidationException(
                    Status.MISSING_ATTRIBUTE.withMessage("Each `dataPoint` must have a non-empty `dataPointId`")
            );
        }
    }

    private void validateAliasRules(HoldingDataPoint dataPoint, Set<String> distinctAliases) {
        String identifier = dataPoint.getAlias() != null
                ? dataPoint.getAlias()
                : dataPoint.getDataPointId();

        // First validate alias format (if present)
        if (dataPoint.getAlias() != null && dataPoint.getAlias().trim().isEmpty()) {
            throw new HoldingValidationException(
                    Status.BLANK_ALIAS.withMessage(dataPoint.getDataPointId() + " must have a non-empty `alias`")
            );
        }

        // Check for duplicates across both aliases and IDs
        if (distinctAliases.contains(identifier)) {
            String errorType = dataPoint.getAlias() != null
                    ? "Alias '" + identifier + "' conflicts with existing identifier"
                    : "Data point ID '" + identifier + "' conflicts with existing identifier";

            throw new HoldingValidationException(
                    Status.DUPLICATE_DATAPOINT.withMessage(errorType)
            );
        }

        distinctAliases.add(identifier);
    }

    private void validateCurrency(HoldingDataPoint dataPoint) {
        if (dataPoint.getCurrency() != null && !isValidCurrency(dataPoint.getCurrency())) {
            throw new HoldingValidationException(
                    Status.INVALID_REQUEST_INPUT.withMessage("Invalid `currency` format in `holdingsDataPoints`")
            );
        }
    }

    private boolean isValidCurrency(String currency) {
        return currency.length() == 3 && StringUtils.isAlpha(currency);
    }
}
