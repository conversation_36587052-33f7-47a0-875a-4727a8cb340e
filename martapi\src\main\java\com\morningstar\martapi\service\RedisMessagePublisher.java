package com.morningstar.martapi.service;

import io.lettuce.core.RedisClient;
import io.lettuce.core.pubsub.StatefulRedisPubSubConnection;
import io.lettuce.core.pubsub.api.sync.RedisPubSubCommands;
import org.springframework.beans.factory.annotation.Value;

import javax.inject.Inject;
import javax.inject.Named;

@Named
public class RedisMessagePublisher {

    private final String configUpdateTopic;
    private final RedisClient redisClient;
    private final String uimTokenClearCacheTopic;

    @Inject
    public RedisMessagePublisher(@Value(value = "${topic.sync}") String configUpdateTopic,
                                 @Value(value = "${topic.uimTokenClearCache}") String uimTokenClearCacheTopic,
                                 @Named("redisClient") RedisClient redisClient) {
        this.configUpdateTopic = configUpdateTopic;
        this.redisClient = redisClient;
        this.uimTokenClearCacheTopic = uimTokenClearCacheTopic;
    }

    public void publishSync(String message) {
        publish(message, configUpdateTopic);
    }

    public void publishUimTokenClearCache(String message) {
        publish(message, uimTokenClearCacheTopic);
    }

    private void publish(String message, String topic) {
        StatefulRedisPubSubConnection<String, String> connection = redisClient.connectPubSub();
        RedisPubSubCommands<String, String> sync = connection.sync();
        sync.publish(topic, message);
        connection.close();
    }
}
