package com.morningstar.martapi.validator.portfolioholdings;

import com.morningstar.dataac.martgateway.data.ph.entity.HoldingDataPoint;
import com.morningstar.dataac.martgateway.data.ph.entity.PortfolioSetting;
import com.morningstar.dataac.martgateway.data.ph.service.PhDataPointInfo;
import com.morningstar.martapi.exception.HoldingValidationException;
import com.morningstar.martcommon.entity.datapoint.DataPoint;
import com.morningstar.martcommon.entity.datapoint.DataPointRepository;
import com.morningstar.dataac.martgateway.data.ph.entity.HoldingDataRequest;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertThrows;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;

public class HoldingsDataPointsRequestFieldsValidatorTest {

    @Before
    public void setUp() {
        addPhId("someId");
        addPhId("dataPoint1");
    }

    @After
    public void tearDown() {
        DataPointRepository.setDataPointMap(new HashMap<>());
    }

    @Test
    public void testValidate_NullHoldingsDataPoints() {
        HoldingDataRequest request = new HoldingDataRequest();
        PortfolioSetting portfolioSetting = new PortfolioSetting();
        portfolioSetting.setHoldingsDataPoints(null); // HoldingsDataPoints is null
        request.setPortfolioSetting(portfolioSetting);

        HoldingsDataPointsRequestFieldsValidator validator = new HoldingsDataPointsRequestFieldsValidator();

        HoldingValidationException exception = assertThrows(
                HoldingValidationException.class,
                () -> validator.validate(request)
        );

        assertEquals("Mandatory field `holdingsDataPoints` is missing or empty", exception.getMessage());
        assertEquals("400", exception.getStatus().getCode().substring(0, 3));
    }

    @Test
    public void testValidate_EmptyHoldingsDataPoints() {
        HoldingDataRequest request = new HoldingDataRequest();
        PortfolioSetting portfolioSetting = new PortfolioSetting();
        portfolioSetting.setHoldingsDataPoints(new ArrayList<>()); // Empty list
        request.setPortfolioSetting(portfolioSetting);

        HoldingsDataPointsRequestFieldsValidator validator = new HoldingsDataPointsRequestFieldsValidator();

        HoldingValidationException exception = assertThrows(
                HoldingValidationException.class,
                () -> validator.validate(request)
        );

        assertEquals("Mandatory field `holdingsDataPoints` is missing or empty", exception.getMessage());
        assertEquals("400", exception.getStatus().getCode().substring(0, 3));
    }

    @Test
    public void testValidate_NullDataPointId() {
        HoldingDataPoint dataPoint = new HoldingDataPoint();
        dataPoint.setDataPointId(null); // Null dataPointId
        dataPoint.setAlias("alias1");
        dataPoint.setCurrency("USD");

        HoldingDataRequest request = createRequestWithDataPoints(dataPoint);

        HoldingsDataPointsRequestFieldsValidator validator = new HoldingsDataPointsRequestFieldsValidator();

        HoldingValidationException exception = assertThrows(
                HoldingValidationException.class,
                () -> validator.validate(request)
        );

        assertEquals("Each `dataPoint` must have a non-empty `dataPointId`", exception.getMessage());
        assertEquals("400", exception.getStatus().getCode().substring(0, 3));
    }

    @Test
    public void testValidate_EmptyDataPointId() {
        HoldingDataPoint dataPoint = new HoldingDataPoint();
        dataPoint.setDataPointId(""); // Empty dataPointId
        dataPoint.setAlias("alias1");
        dataPoint.setCurrency("USD");

        HoldingDataRequest request = createRequestWithDataPoints(dataPoint);

        HoldingsDataPointsRequestFieldsValidator validator = new HoldingsDataPointsRequestFieldsValidator();

        HoldingValidationException exception = assertThrows(
                HoldingValidationException.class,
                () -> validator.validate(request)
        );

        assertEquals("Each `dataPoint` must have a non-empty `dataPointId`", exception.getMessage());
        assertEquals("400", exception.getStatus().getCode().substring(0, 3));
    }

    @Test
    public void testValidate_DuplicateDataPointId() {
        HoldingDataPoint dataPoint1 = new HoldingDataPoint();
        dataPoint1.setDataPointId("someId");

        HoldingDataPoint dataPoint2 = new HoldingDataPoint();
        dataPoint2.setDataPointId("someId");

        HoldingDataRequest request = createRequestWithDataPoints(dataPoint1, dataPoint2);

        HoldingsDataPointsRequestFieldsValidator validator = new HoldingsDataPointsRequestFieldsValidator();

        HoldingValidationException exception = assertThrows(
                HoldingValidationException.class,
                () -> validator.validate(request)
        );

        assertEquals("Data point ID 'someId' conflicts with existing identifier", exception.getMessage());
        assertEquals("400", exception.getStatus().getCode().substring(0, 3));
    }

    @Test
    public void testValidate_DuplicateDataPointIdWithSameAlias() {
        HoldingDataPoint dataPoint1 = new HoldingDataPoint();
        dataPoint1.setDataPointId("someId");
        dataPoint1.setAlias("alias1");

        HoldingDataPoint dataPoint2 = new HoldingDataPoint();
        dataPoint2.setDataPointId("someId");
        dataPoint2.setAlias("alias1");

        HoldingDataRequest request = createRequestWithDataPoints(dataPoint1, dataPoint2);

        HoldingsDataPointsRequestFieldsValidator validator = new HoldingsDataPointsRequestFieldsValidator();

        HoldingValidationException exception = assertThrows(
                HoldingValidationException.class,
                () -> validator.validate(request)
        );

        assertEquals("Alias 'alias1' conflicts with existing identifier", exception.getMessage());
        assertEquals("400", exception.getStatus().getCode().substring(0, 3));
    }

    @Test
    public void testValidate_DuplicateDataPointIdWithDifferentAlias() {
        HoldingDataPoint dataPoint1 = new HoldingDataPoint();
        dataPoint1.setDataPointId("someId");
        dataPoint1.setAlias("alias1");

        HoldingDataPoint dataPoint2 = new HoldingDataPoint();
        dataPoint2.setDataPointId("someId");
        dataPoint2.setAlias("alias2");

        HoldingDataRequest request = createRequestWithDataPoints(dataPoint1, dataPoint2);
        HoldingsDataPointsRequestFieldsValidator validator = new HoldingsDataPointsRequestFieldsValidator();
        assertDoesNotThrow(() -> validator.validate(request));
    }

    @Test
    public void testValidate_DuplicateDataPointIdWithOneAliasAndOneWithout() {
        //Testing first duplicate id has alias and second duplicate id has no alias
        HoldingDataPoint dataPoint1 = new HoldingDataPoint();
        dataPoint1.setDataPointId("someId");
        dataPoint1.setAlias("alias1");
        HoldingDataPoint dataPoint2 = new HoldingDataPoint();
        dataPoint2.setDataPointId("someId");
        HoldingDataRequest request1 = createRequestWithDataPoints(dataPoint1, dataPoint2);
        HoldingsDataPointsRequestFieldsValidator validator1 = new HoldingsDataPointsRequestFieldsValidator();
        assertDoesNotThrow(() -> validator1.validate(request1));

        //Testing first duplicate id has no alias and second duplicate id has alias
        HoldingDataPoint dataPoint3 = new HoldingDataPoint();
        dataPoint3.setDataPointId("someId");
        HoldingDataPoint dataPoint4 = new HoldingDataPoint();
        dataPoint4.setDataPointId("someId");
        dataPoint4.setAlias("alias1");
        HoldingDataRequest request2 = createRequestWithDataPoints(dataPoint3, dataPoint4);
        HoldingsDataPointsRequestFieldsValidator validator2 = new HoldingsDataPointsRequestFieldsValidator();
        assertDoesNotThrow(() -> validator2.validate(request2));
    }

    @Test
    public void testValidate_EmptyAlias() {
        HoldingDataPoint dataPoint = new HoldingDataPoint();
        dataPoint.setDataPointId("dataPoint1");
        dataPoint.setAlias(""); // Empty alias
        dataPoint.setCurrency("USD");

        HoldingDataRequest request = createRequestWithDataPoints(dataPoint);

        HoldingsDataPointsRequestFieldsValidator validator = new HoldingsDataPointsRequestFieldsValidator();

        HoldingValidationException exception = assertThrows(
                HoldingValidationException.class,
                () -> validator.validate(request)
        );

        assertEquals("dataPoint1 must have a non-empty `alias`", exception.getMessage());
        assertEquals("400", exception.getStatus().getCode().substring(0, 3));
    }

    @Test
    public void testValidate_DuplicateAlias() {
        HoldingDataPoint dataPoint1 = new HoldingDataPoint();
        dataPoint1.setDataPointId("someId");
        dataPoint1.setAlias("test1");

        HoldingDataPoint dataPoint2 = new HoldingDataPoint();
        dataPoint2.setDataPointId("dataPoint1");
        dataPoint2.setAlias("test1");

        HoldingDataRequest request = createRequestWithDataPoints(dataPoint1, dataPoint2);

        HoldingsDataPointsRequestFieldsValidator validator = new HoldingsDataPointsRequestFieldsValidator();

        HoldingValidationException exception = assertThrows(
                HoldingValidationException.class,
                () -> validator.validate(request)
        );

        assertEquals("Alias 'test1' conflicts with existing identifier", exception.getMessage());
        assertEquals("400", exception.getStatus().getCode().substring(0, 3));
    }

    @Test
    public void testValidate_DataPointIdConflictsWithExistingAlias() {
        // First data point uses alias equal to another data point's ID
        HoldingDataPoint dataPoint1 = new HoldingDataPoint();
        dataPoint1.setDataPointId("someId");
        dataPoint1.setAlias("SHARED_ID");

        // Second data point uses ID that matches first data point's alias
        HoldingDataPoint dataPoint2 = new HoldingDataPoint();
        dataPoint2.setDataPointId("SHARED_ID");

        HoldingDataRequest request = createRequestWithDataPoints(dataPoint1, dataPoint2);

        HoldingsDataPointsRequestFieldsValidator validator = new HoldingsDataPointsRequestFieldsValidator();

        HoldingValidationException exception = assertThrows(
                HoldingValidationException.class,
                () -> validator.validate(request)
        );

        assertEquals("Data point ID 'SHARED_ID' conflicts with existing identifier", exception.getMessage());
        assertEquals("400", exception.getStatus().getCode().substring(0, 3));
    }

    @Test
    public void testValidate_AliasConflictsWithExistingDataPointId() {
        // First data point has no alias
        HoldingDataPoint dataPoint1 = new HoldingDataPoint();
        dataPoint1.setDataPointId("SHARED_ID");
        addPhId("SHARED_ID");

        // Second data point uses alias matching first data point's ID
        HoldingDataPoint dataPoint2 = new HoldingDataPoint();
        dataPoint2.setDataPointId("someId");
        dataPoint2.setAlias("SHARED_ID");

        HoldingDataRequest request = createRequestWithDataPoints(dataPoint1, dataPoint2);

        HoldingsDataPointsRequestFieldsValidator validator = new HoldingsDataPointsRequestFieldsValidator();

        HoldingValidationException exception = assertThrows(
                HoldingValidationException.class,
                () -> validator.validate(request)
        );

        assertEquals("Alias 'SHARED_ID' conflicts with existing identifier", exception.getMessage());
        assertEquals("400", exception.getStatus().getCode().substring(0, 3));
    }

    @Test
    public void testValidate_InvalidCurrency_NullCurrency() {
        HoldingDataPoint dataPoint = new HoldingDataPoint();
        dataPoint.setDataPointId("dataPoint1");
        dataPoint.setAlias("alias1");
        dataPoint.setCurrency(null); // Null currency

        HoldingDataRequest request = createRequestWithDataPoints(dataPoint);

        HoldingsDataPointsRequestFieldsValidator validator = new HoldingsDataPointsRequestFieldsValidator();

        assertDoesNotThrow(() -> validator.validate(request));
    }

    @Test
    public void testValidate_InvalidCurrency_WrongLength() {
        HoldingDataPoint dataPoint = new HoldingDataPoint();
        dataPoint.setDataPointId("dataPoint1");
        dataPoint.setCurrency("US");

        HoldingDataRequest request = createRequestWithDataPoints(dataPoint);

        HoldingsDataPointsRequestFieldsValidator validator = new HoldingsDataPointsRequestFieldsValidator();

        HoldingValidationException exception = assertThrows(
                HoldingValidationException.class,
                () -> validator.validate(request)
        );

        assertEquals("Invalid `currency` format in `holdingsDataPoints`", exception.getMessage());
        assertEquals("400", exception.getStatus().getCode().substring(0, 3));
    }

    @Test
    public void testValidate_InvalidCurrency_NonAlpha() {
        HoldingDataPoint dataPoint = new HoldingDataPoint();
        dataPoint.setDataPointId("dataPoint1");
        dataPoint.setAlias("alias1");
        dataPoint.setCurrency("US1"); // Currency contains non-alpha character

        HoldingDataRequest request = createRequestWithDataPoints(dataPoint);

        HoldingsDataPointsRequestFieldsValidator validator = new HoldingsDataPointsRequestFieldsValidator();

        HoldingValidationException exception = assertThrows(
                HoldingValidationException.class,
                () -> validator.validate(request)
        );

        assertEquals("Invalid `currency` format in `holdingsDataPoints`", exception.getMessage());
        assertEquals("400", exception.getStatus().getCode().substring(0, 3));
    }

    @Test
    public void testValidate_ValidDataPoints() {
        // Setup
        HoldingDataPoint dataPoint1 = new HoldingDataPoint();
        dataPoint1.setDataPointId("dataPoint1");
        dataPoint1.setAlias("alias1");
        dataPoint1.setCurrency("USD");

        HoldingDataPoint dataPoint2 = new HoldingDataPoint();
        dataPoint2.setDataPointId("dataPoint2");
        dataPoint2.setAlias("alias2");
        dataPoint2.setCurrency("EUR");
        addPhId("dataPoint2");

        HoldingDataRequest request = createRequestWithDataPoints(dataPoint1, dataPoint2);

        HoldingsDataPointsRequestFieldsValidator validator = new HoldingsDataPointsRequestFieldsValidator();

        assertDoesNotThrow(() -> validator.validate(request));
    }

    @Test
    public void testValidate_isPhDp() {
        HoldingDataPoint dataPoint = new HoldingDataPoint();
        dataPoint.setDataPointId("notPhDataPoint");
        HoldingDataRequest request = createRequestWithDataPoints(dataPoint);
        HoldingsDataPointsRequestFieldsValidator validator = new HoldingsDataPointsRequestFieldsValidator();
        HoldingValidationException exception = assertThrows(
                HoldingValidationException.class,
                () -> validator.validate(request)
        );

        assertEquals("Data point ID 'notPhDataPoint' is not a portfolio holding data point", exception.getMessage());
        assertEquals("400", exception.getStatus().getCode());
    }

    @Test
    public void testValidateIceIsValid() {
        HoldingDataPoint iceHoldingDataPoint = new HoldingDataPoint();
        iceHoldingDataPoint.setDataPointId("iceDataPointId");
        HoldingDataRequest request = createRequestWithDataPoints(iceHoldingDataPoint);

        // do without ICEFI source
        HoldingsDataPointsRequestFieldsValidator validator = new HoldingsDataPointsRequestFieldsValidator();
        HoldingValidationException exception = assertThrows(
                HoldingValidationException.class,
                () -> validator.validate(request)
        );
        assertEquals("Data point ID 'iceDataPointId' is not a portfolio holding data point", exception.getMessage());
        assertEquals("400", exception.getStatus().getCode());

        // add ICEFI source
        DataPoint iceDataPoint = DataPoint.builder().id("iceDataPointId").nid("iceDataPointId").src("ICEFI").build();
        DataPointRepository.setDataPointMap(Map.of(iceDataPoint.getId(), iceDataPoint));
        assertDoesNotThrow(() -> validator.validate(request));
    }

    private HoldingDataRequest createRequestWithDataPoints(HoldingDataPoint... dataPoints) {
        HoldingDataRequest request = new HoldingDataRequest();
        PortfolioSetting portfolioSetting = new PortfolioSetting();
        List<HoldingDataPoint> dataPointsList = new ArrayList<>(Arrays.asList(dataPoints));
        portfolioSetting.setHoldingsDataPoints(dataPointsList);
        request.setPortfolioSetting(portfolioSetting);
        return request;
    }

    private void addPhId(String id) {
        DataPoint dp = DataPoint.builder().id(id).build();
        PhDataPointInfo.addBaseLevelPathToDataPointEntry(dp);
    }

}