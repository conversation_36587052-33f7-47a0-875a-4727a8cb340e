package com.morningstar.martapi.config;

import io.lettuce.core.RedisClient;
import io.lettuce.core.pubsub.StatefulRedisPubSubConnection;
import io.lettuce.core.pubsub.api.sync.RedisPubSubCommands;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class RedisMessageConfigTest {

    @Mock
    private StatefulRedisPubSubConnection<String, String> mockConnection;
    @Mock
    private RedisPubSubCommands<String, String> mockCommands;
    @Mock
    private RedisClient mockClient;
    @InjectMocks
    private RedisMessageConfig redisMessageConfig;

    @Before
    public void setUp() {
        when(mockClient.connectPubSub()).thenReturn(mockConnection);
        when(mockConnection.sync()).thenReturn(mockCommands);
    }

    @Test
    public void subscribeTopic() {
        redisMessageConfig.subscribeTopic();
        verify(mockCommands, Mockito.times(4)).subscribe(null);
    }
}
