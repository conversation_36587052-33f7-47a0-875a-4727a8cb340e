package com.morningstar.martapi.service;

import static com.morningstar.martapi.config.Constant.FAIL;
import static com.morningstar.martapi.config.Constant.RUNNING;
import static com.morningstar.martapi.config.Constant.SUBMITTED;
import static com.morningstar.martapi.config.Constant.SUCCESS;

import com.amazonaws.SdkClientException;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.morningstar.martapi.entity.AsyncApiResponseEntity;
import com.morningstar.martapi.entity.AsyncCacheMessage;
import com.morningstar.martapi.entity.S3Url;
import com.morningstar.martapi.exception.InvestmentApiValidationException;
import com.morningstar.martapi.repo.DynamoDao;
import com.morningstar.martapi.util.AsyncGetStatusUserIdUtil;
import com.morningstar.martapi.util.BatchSizeUtil;
import com.morningstar.martgateway.domains.core.entity.response.Status;
import com.morningstar.martgateway.interfaces.model.AsyncDbDetails;
import com.morningstar.martgateway.interfaces.model.AsyncInput;
import com.morningstar.martgateway.interfaces.model.AsyncInput.ApiType;
import com.morningstar.martgateway.util.JsonUtils;
import com.mysql.cj.util.StringUtils;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import javax.inject.Inject;
import javax.inject.Named;

import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import software.amazon.awssdk.services.s3.presigner.model.PresignedGetObjectRequest;

@Named
public class AsyncApiService {

    private static final String GRIDVIEW_PREFIX = "gridview/";
    private static final String PH_PREFIX = "ph/";
    private static final DateTimeFormatter dateFormatter = DateTimeFormatter.ISO_LOCAL_DATE_TIME;
    private static final Logger log = LoggerFactory.getLogger(AsyncApiService.class);

    private final RedisService redisService;
    private final S3Service s3Service;
    private final LambdaService lambdaService;
    private final AsyncGetStatusUserIdUtil asyncGetStatusUserIdUtil;

    private final DynamoDao dynamoDao;
    private final String environment;
    private final String s3Region;


    @Inject
    public AsyncApiService(
            RedisService redisService,
            S3Service s3Service,
            LambdaService lambdaService,
            AsyncGetStatusUserIdUtil asyncGetStatusUserIdUtil,
            DynamoDao dynamoDao,
            @Value("${spring.profiles}") String environment,
            @Value("${aws.region}") String s3Region
    ) {
        this.redisService = redisService;
        this.s3Service = s3Service;
        this.lambdaService = lambdaService;
        this.asyncGetStatusUserIdUtil = asyncGetStatusUserIdUtil;
        this.dynamoDao = dynamoDao;
        this.environment = environment;
        this.s3Region = s3Region;
    }

    public AsyncApiResponseEntity fetchAsyncData(AsyncInput asyncInput) {
        BatchSizeUtil batchSizeUtil = new BatchSizeUtil(asyncInput);
        String startTime = getLocalDateTime();
        Instant instant = Instant.now();
        String productId = asyncInput.getProductId();
        String userId = asyncInput.getUserId();

        int idSize = asyncInput.getIdSize();
        int dpsCount = asyncInput.getDpsCount();
        String jobId = addDatabaseEntry(productId, userId, String.valueOf(idSize), String.valueOf(dpsCount), startTime, asyncInput.getApiType(), batchSizeUtil.getBlBatchSize(), batchSizeUtil.getLtBatchSize());
        asyncInput.setJobId(jobId);
        log.info("event_type=\"job created\", event_description=\"Starting Async Mart Api Job\", job_id=\"{}\" api_type=\"{}\"", jobId, asyncInput.getApiType());

        AsyncCacheMessage cacheMessage = AsyncCacheMessage.builder()
                .userId(userId)
                .productId(productId)
                .status(SUBMITTED)
                .startTime(startTime)
                .environment(environment)
                .apiType(asyncInput.getApiType())
                .s3region(s3Region)
                .blBatchSize(batchSizeUtil.getBlBatchSize())
                .ltBatchSize(batchSizeUtil.getLtBatchSize())
                .build();
        redisService.addRedisKey(jobId, cacheMessage);
        log.info("Create job {} and update status in DB and Redis, total time: {}ms",
                jobId, Duration.between(instant, Instant.now()).toMillis());

        String key = getKey(jobId, asyncInput.getApiType());
        try {
            log.info("event_type=\"file upload\", event_description=\"Starting input file creation\", job_id=\"{}\", api_type=\"{}\"", jobId, asyncInput.getApiType());
            String input = JsonUtils.toJsonString(asyncInput);
            ObjectMetadata objectMetadata = new ObjectMetadata();
            objectMetadata.addUserMetadata("apiType", asyncInput.getApiType().name());
            s3Service.putObject(key, input, objectMetadata);
            lambdaService.invokeLambda(jobId, asyncInput.getApiType());
            AsyncApiResponseEntity response = AsyncApiResponseEntity.builder()
                    .jobId(jobId)
                    .jobStatus(SUBMITTED)
                    .startTime(startTime)
                    .build();
            log.info("event_type=\"file upload\", event_description=\"file upload complete, Async Mart Api input file was sent to s3\", file_name=\"{}\", job_id=\"{}\", api_type=\"{}\"", key, jobId, asyncInput.getApiType());
            return response;
        } catch (SdkClientException e) {
            log.warn("event_type=\"file upload failure\", event_description=\"file upload failed, Async Mart Api encountered an error\", file_name=\"{}\", job_id=\"{}\", api_type=\"{}\", error_message=\"{}\"", key, jobId, asyncInput.getApiType(), e.getMessage());
            throw new InvestmentApiValidationException(Status.INTERNAL_ERROR);
        }
    }

    private static String getKey(String jobId, ApiType apiType) {
        String prefix = apiType == ApiType.INVESTMENT_API ? GRIDVIEW_PREFIX : PH_PREFIX;
        return String.format("%s%s.json", prefix,jobId);
    }

    private String addDatabaseEntry(String productId, String userId, String idSize, String dpsCount,
            String startTime, ApiType apiType, Integer blBatchSize, Integer ltBatchSize) {
        String jobId = UUID.randomUUID().toString();
        AsyncDbDetails dbEntry = AsyncDbDetails.builder()
                .userId(userId)
                .productId(productId)
                .jobId(jobId)
                .status(SUBMITTED)
                .idSize(Integer.valueOf(idSize))
                .dpsCount(Integer.valueOf(dpsCount))
                .startTime(startTime)
                .updateTime(startTime)
                .environment(environment)
                .s3Region(s3Region)
                .apiType(apiType.name())
                .blBatchSize(blBatchSize)
                .ltBatchSize(ltBatchSize)
                .build();
        dynamoDao.upsertAsyncRecord(dbEntry);
        return jobId;
    }

    public AsyncApiResponseEntity getGridviewStatus(String productId, String bearerToken, String jobId, String headerUserId) {
        String userId = asyncGetStatusUserIdUtil.determineUserId(bearerToken, headerUserId);

        String redisValue = redisService.getRedisKey(jobId);
        AsyncApiResponseEntity response;

        if (StringUtils.isNullOrEmpty(redisValue)) {
            response = processStatusResponseFromDb(userId, jobId, productId, null);
        } else {
            response = processStatusResponseFromCache(redisValue, userId, productId, jobId, null);
        }
        response.setJobId(jobId);
        response.setProductId(productId);

        if (response.getJobStatus().equalsIgnoreCase(RUNNING) && redisService.isJobInactive(jobId)) {
            updateInactiveJob(response, jobId, userId);
        }

        if (response.getJobStatus() != null && response.getJobStatus().equals(SUCCESS)) {
            if (StringUtils.isNullOrEmpty(response.getUrl())) {
                try {
                    PresignedGetObjectRequest preSignedUrl = s3Service.getPresignedUrl(jobId + ".ndjson", response.getS3regionOfData());
                    response.setUrl(preSignedUrl.url().toString());
                    response.setUrlExpireTime(convertExpiration(preSignedUrl.expiration()));
                    updateCacheMessage(jobId, userId, productId, response);
                } catch (SdkClientException e) {
                    log.warn("event_type=\"get S3 presigned Url failure\", event_description=\"Async Mart API failed to fetch s3Url\", job_id=\"{}\", error_message=\"{}\"", jobId, e.getMessage());
                    throw new InvestmentApiValidationException(Status.INTERNAL_ERROR);
                }
            }
            return response;
        }
        return response;
    }

    public AsyncApiResponseEntity getPHStatus(String productId, String bearerToken, String jobId, String headerUserId, List<Integer> pages) {
        String userId = asyncGetStatusUserIdUtil.determineUserId(bearerToken, headerUserId);

        String redisValue = redisService.getRedisKey(jobId);
        String totalPages = getTotalPages(redisService.getTotalPages(jobId));
        AsyncApiResponseEntity response;

        if (StringUtils.isNullOrEmpty(redisValue)) {
            response = processStatusResponseFromDb(userId, jobId, productId, totalPages);
        } else {
            response = processStatusResponseFromCache(redisValue, userId, productId, jobId, totalPages);
        }
        response.setJobId(jobId);
        response.setProductId(productId);

        if (response.getJobStatus().equalsIgnoreCase(RUNNING)) {
            if (redisService.isJobInactive(jobId)) {
                updateInactiveJob(response, jobId, userId);
            } else {
                response.setS3Urls(getS3Urls(response, jobId, pages, s3Service));
                return response;
            }
        }

        if (response.getJobStatus() != null && response.getJobStatus().equals(SUCCESS)) {
            updateCacheMessage(jobId, userId, productId, response);
            response.setS3Urls(getS3Urls(response, jobId, pages, s3Service));
            return response;
        }
        return response;
    }

    private AsyncApiResponseEntity processStatusResponseFromCache(String redisValue, String userId, String productId, String jobId, String totalPages) {
        AsyncCacheMessage cacheMessage = JsonUtils.fromJsonString(redisValue, AsyncCacheMessage.class);
        if (!environment.equals(cacheMessage.getEnvironment())) {
            return processStatusResponseFromDb(userId, jobId, productId, totalPages);
        }
        if (StringUtils.isNullOrEmpty(cacheMessage.getUserId()) || !cacheMessage.getUserId().equals(userId)) {
            throw new InvestmentApiValidationException(Status.UNAUTHORIZED_USER_FOR_JOB_ID);
        }
        if (StringUtils.isNullOrEmpty(cacheMessage.getProductId()) || !cacheMessage.getProductId().equals(productId)) {
            throw new InvestmentApiValidationException(Status.PRODUCT_ID_MISMATCH);
        }
        if (totalPages == null && !StringUtils.isNullOrEmpty(cacheMessage.getTotalPages()) && Integer.parseInt(cacheMessage.getTotalPages()) > 0) {
            totalPages = cacheMessage.getTotalPages();
        }
        return AsyncApiResponseEntity.builder()
                .startTime(cacheMessage.getStartTime())
                .endTime(cacheMessage.getEndTime())
                .jobStatus(cacheMessage.getStatus())
                .urlExpireTime(cacheMessage.getUrlExpireTime())
                .url(cacheMessage.getUrl())
                .errorMessage(cacheMessage.getErrorMessage())
                .apiType(cacheMessage.getApiType())
                .totalPages(totalPages)
                .s3regionOfData(cacheMessage.getS3region())
                .build();
    }

    private AsyncApiResponseEntity processStatusResponseFromDb(String userId, String jobId, String productId, String totalPages) {
        AsyncDbDetails details = dynamoDao.getAsyncRecord(jobId);
        if (details == null) {
            throw new InvestmentApiValidationException(Status.JOB_ID_NOT_FOUND);
        }
        if (totalPages == null && details.getTotalPages() != null && details.getTotalPages() > 0) {
            totalPages = String.valueOf(details.getTotalPages());
        }
        AsyncCacheMessage cacheMessage = AsyncCacheMessage.builder()
                .userId(details.getUserId())
                .productId(details.getProductId())
                .status(details.getStatus())
                .startTime(details.getStartTime())
                .endTime(details.getEndTime())
                .errorMessage(details.getErrorMessage())
                .environment(environment)
                .apiType(ApiType.getByStringWithDefault(details.getApiType(), ApiType.INVESTMENT_API))
                .totalPages(totalPages)
                .s3region(details.getS3Region())
                .build();
        redisService.addRedisKey(jobId, cacheMessage);
        if (StringUtils.isNullOrEmpty(details.getUserId()) || !details.getUserId().equals(userId)) {
            throw new InvestmentApiValidationException(Status.UNAUTHORIZED_USER_FOR_JOB_ID);
        }
        if (StringUtils.isNullOrEmpty(details.getProductId()) || !details.getProductId().equals(productId)) {
            throw new InvestmentApiValidationException(Status.PRODUCT_ID_MISMATCH);
        }

        return AsyncApiResponseEntity.builder()
                .startTime(details.getStartTime())
                .endTime(details.getEndTime())
                .jobStatus(details.getStatus())
                .errorMessage(details.getErrorMessage())
                .apiType(ApiType.getByStringWithDefault(details.getApiType(), ApiType.INVESTMENT_API))
                .totalPages(totalPages)
                .s3regionOfData(details.getS3Region())
                .build();
    }

    private void  updateInactiveJob(AsyncApiResponseEntity response, String jobId, String userId) {
        String endTime = getLocalDateTime();
        response.setJobStatus(FAIL);
        response.setEndTime(endTime);
        response.setErrorMessage("Job processing timeout");
        AsyncCacheMessage cacheMessage = AsyncCacheMessage.builder()
                .userId(userId)
                .productId(response.getProductId())
                .status(response.getJobStatus())
                .startTime(response.getStartTime())
                .endTime(response.getEndTime())
                .errorMessage(response.getErrorMessage())
                .environment(environment)
                .apiType(response.getApiType())
                .totalPages(response.getTotalPages())
                .s3region(response.getS3regionOfData())
                .build();
        redisService.addRedisKey(jobId, cacheMessage);
        AsyncDbDetails asyncDbDetails = dynamoDao.getAsyncRecord(jobId);
        asyncDbDetails.setStatus(response.getJobStatus());
        asyncDbDetails.setEndTime(response.getEndTime());
        asyncDbDetails.setErrorMessage(response.getErrorMessage());
        asyncDbDetails.setTotalPages(Optional.ofNullable(response.getTotalPages()).map(Integer::parseInt).orElse(null));
        dynamoDao.upsertAsyncRecord(asyncDbDetails);
    }

    private List<S3Url> getS3Urls(AsyncApiResponseEntity response, String jobId, List<Integer> pages, S3Service s3Service) {
        if (StringUtils.isNullOrEmpty(response.getTotalPages())) {
            return null;
        }
        List<S3Url> s3Urls = new ArrayList<>();
        if (CollectionUtils.isEmpty(pages)) {
            // Get all pages
            for (int i = 1; i <= Integer.parseInt(response.getTotalPages()); i++) {
                String key = String.format("%s/%d", jobId, i);
                addS3Url(s3Urls, jobId, key, i, s3Service, response.getS3regionOfData());
            }
        } else {
            // Get specific pages
            for (int page : pages) {
                if (page > 0 && page <= Integer.parseInt(response.getTotalPages())) {
                    String key = String.format("%s/%d", jobId, page);
                    addS3Url(s3Urls, jobId, key, page, s3Service, response.getS3regionOfData());
                }
            }
        }
        return s3Urls;
    }

    private void addS3Url(List<S3Url> s3Urls, String jobId, String key, int page, S3Service s3Service, String s3regionOfData) {
        try {
            PresignedGetObjectRequest presignedUrl = s3Service.getPresignedUrl(key + ".ndjson.gz", s3regionOfData);
            s3Urls.add(new S3Url(String.valueOf(page), presignedUrl.url().toString(), convertExpiration(presignedUrl.expiration())));
        } catch (SdkClientException e) {
            log.error("event_type=\"get S3 presigned Url failure\", event_description=\"Async Mart API failed to fetch s3Url\", job_id=\"{}\", error_message=\"{}\"", jobId, e.getMessage());
            throw new InvestmentApiValidationException(Status.INTERNAL_ERROR);
        }
    }

    private String getTotalPages(String pagesValue) {
        if (!StringUtils.isNullOrEmpty(pagesValue) && Integer.parseInt(pagesValue) > 0) {
            return pagesValue;
        }
        return null;
    }

    private void updateCacheMessage(String jobId, String userId, String productId, AsyncApiResponseEntity response) {
        AsyncCacheMessage cacheMessage = AsyncCacheMessage.builder()
                .userId(userId)
                .productId(productId)
                .status(response.getJobStatus())
                .startTime(response.getStartTime())
                .endTime(response.getEndTime())
                .url(response.getUrl())
                .urlExpireTime(response.getUrlExpireTime())
                .environment(environment)
                .apiType(response.getApiType())
                .totalPages(response.getTotalPages())
                .s3region(response.getS3regionOfData())
                .build();
        redisService.addRedisKey(jobId, cacheMessage);
    }

    private String getLocalDateTime() {
        LocalDateTime localTime = ZonedDateTime.now(ZoneOffset.UTC).toLocalDateTime().truncatedTo(ChronoUnit.SECONDS);
        return dateFormatter.format(localTime);
    }

    private static String convertExpiration(Instant expiration) {
        return dateFormatter.withZone(ZoneOffset.UTC).format(expiration);
    }
}
