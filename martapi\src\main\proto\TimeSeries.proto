syntax = "proto3";

package investments;

import "TsCacheData.proto";

option java_multiple_files = true;
option java_package = "com.morningstar.martapi.grpc";
option java_outer_classname = "TimeSeriesServiceProto";

service TimeSeriesService {
  rpc RetrieveTimeSeriesDataAsProtobuf(TimeSeriesRequest) returns (protobuf.TimeSeriesDatas);
  rpc RetrieveTimeSeriesData(TimeSeriesRequest) returns (OldTimeSeriesResponse);
  rpc GetInvestmentTSData(TimeSeriesRequest) returns (TimeSeriesResponse);
}


message TimeSeriesRequest {
  repeated string investmentIds = 1;
  repeated string dataPoints = 2;
  string startDate = 3;
  string endDate = 4;
  string currency = 5;
  string preCurrency = 6;
  string readCache = 7;
  string dateFormat = 8;
  string decimalFormat = 9;
  string extendPerformance = 10;
  string postTax = 11;
  bool useRequireId = 12;
  string useCase = 13;
  bool useNewCcs = 14;
  string productId = 15;
  string requestId = 16;
  string userId = 17;
  string authorization = 18;
  bool checkEntitlement = 19;
  string entitlementProductId = 20;
}

message TimeSeriesPoint {
  string date = 1;
  string value = 2;
}

message DataPoint {
  string datapointId = 1;
  repeated TimeSeriesPoint timeSeriesData = 2;
}

message Investment {
  string id = 1;
  repeated DataPoint values = 2;
}

message TimeSeriesResponse {
  repeated Investment investments = 1;
}

// ts format = 1
message OldTimeSeriesResponse {
  Status s = 1;
  Content c = 2;
}

message Status {
  string code = 1;
  string msg = 2;
}

message Content {
  repeated Item i = 1;
}

message Item {
  string secid = 1;
  string dataid = 2;
  repeated DataPointValue data = 3;
}

message DataPointValue {
  int32 d = 1;
  double v = 2;
}
