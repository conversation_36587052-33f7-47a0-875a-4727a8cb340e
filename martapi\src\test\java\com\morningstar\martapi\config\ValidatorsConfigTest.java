package com.morningstar.martapi.config;

import com.morningstar.dataac.martgateway.core.entitlement.service.DataEntitlementService;
import com.morningstar.martapi.exception.InvestmentApiValidationException;
import com.morningstar.martapi.exception.ValidationException;
import com.morningstar.martapi.validator.RequestValidationHandler;
import com.morningstar.martapi.validator.entity.HeadersAndParams;
import com.morningstar.martcommon.entity.ClearCacheRequest;
import com.morningstar.martcommon.entity.result.request.MartRequest;
import com.morningstar.martgateway.infrastructures.config.ProductIdsRegistry;
import com.morningstar.martgateway.interfaces.model.investmentapi.InvestmentApiRequest;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;

@ExtendWith(MockitoExtension.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class ValidatorsConfigTest {

    @Mock
    private ProductIdsRegistry productIdsRegistry;
    @Mock
    private DataEntitlementService<InvestmentApiRequest> dataEntitlementService;

    @Test
    public void investmentApiValidator() {
        ValidatorsConfig config  = new ValidatorsConfig();
        RequestValidationHandler<HeadersAndParams, InvestmentApiRequest> validator =  config.investmentApiValidator(productIdsRegistry);
        assertThrows(
                InvestmentApiValidationException.class, () -> validator.validateHeadersAndParams(HeadersAndParams.builder().authorizationToken("").build()));
        assertThrows(InvestmentApiValidationException.class, () -> validator.validateRequestBody(new InvestmentApiRequest()));
    }

    @Test
    public void timeSeriesApiValidator() {
        ValidatorsConfig config  = new ValidatorsConfig();
        RequestValidationHandler<HeadersAndParams, MartRequest> validator =  config.timeSeriesApiValidator(productIdsRegistry);
        assertThrows(
                ValidationException.class, () -> validator.validateHeadersAndParams(HeadersAndParams.builder().authorizationToken("").build()));
        assertThrows(ValidationException.class, () -> validator.validateRequestBody(new MartRequest()));
    }

    @Test
    public void getAsyncDataApiValidator() {
        ValidatorsConfig config  = new ValidatorsConfig();
        ProductIdsRegistry productIdsRegistry = new ProductIdsRegistry();
        RequestValidationHandler<HeadersAndParams, InvestmentApiRequest> validator = config.asyncDataApiValidator(productIdsRegistry, dataEntitlementService);
        assertNotNull(validator);
    }

    @Test
    public void getClearCacheValidator() {
        ValidatorsConfig config  = new ValidatorsConfig();
        RequestValidationHandler<HeadersAndParams, ClearCacheRequest> validator = config.clearCacheValidator();
        assertNotNull(validator);
    }

    @Test
    public void getDeltaDetectionValidator() {
        ValidatorsConfig config  = new ValidatorsConfig();
        RequestValidationHandler<HeadersAndParams, InvestmentApiRequest> validator = config.deltaDetectionApiValidator(productIdsRegistry);
        assertNotNull(validator);
    }
}
