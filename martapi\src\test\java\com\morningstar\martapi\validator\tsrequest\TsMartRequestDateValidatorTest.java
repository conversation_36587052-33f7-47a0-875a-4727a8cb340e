package com.morningstar.martapi.validator.tsrequest;

import com.morningstar.martapi.exception.ValidationException;
import com.morningstar.martcommon.entity.result.request.MartRequest;
import com.morningstar.martgateway.domains.core.entity.response.Status;
import java.util.List;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;

public class TsMartRequestDateValidatorTest {
	private TsMartRequestDateValidator validator;

	@Before
	public void setup() {
		this.validator = new TsMartRequestDateValidator();
	}

	@Test
	public void endDateBeforeStartDate() {
		MartRequest request = MartRequest.builder()
				.useCase("feed")
				.ids(List.of("OP01010", "OP01012"))
				.dps(List.of("HP010", "HP022"))
				.startDate("2022-01-01")
				.endDate("2021-01-01")
				.build();
		ValidationException exception = Assertions.assertThrows(ValidationException.class,
				() -> validator.validate(request));
		Assertions.assertEquals(Status.INVALID_DATE_SETTING.getCode(), exception.getStatus().getCode());
	}

	@Test
	public void missingStartDate() {
		MartRequest request = MartRequest.builder()
				.useCase("feed")
				.ids(List.of("OP01010", "OP01012"))
				.dps(List.of("HP010", "HP022"))
				.endDate("2021-01-01")
				.build();
		ValidationException exception = Assertions.assertThrows(ValidationException.class,
				() -> validator.validate(request));
		Assertions.assertEquals(Status.INVALID_DATE_SETTING.getCode(), exception.getStatus().getCode());
	}

	@Test
	public void missingEndDate() {
		MartRequest request = MartRequest.builder()
				.useCase("feed")
				.ids(List.of("OP01010", "OP01012"))
				.dps(List.of("HP010", "HP022"))
				.startDate("2021-01-01")
				.build();
		ValidationException exception = Assertions.assertThrows(ValidationException.class,
				() -> validator.validate(request));
		Assertions.assertEquals(Status.INVALID_DATE_SETTING.getCode(), exception.getStatus().getCode());
	}

	@Test
	public void invalidDateFormat() {
		MartRequest request = MartRequest.builder()
				.useCase("feed")
				.ids(List.of("OP01010", "OP01012"))
				.dps(List.of("HP010", "HP022"))
				.startDate("2021/01/01")
				.endDate("2022/01/01")
				.build();
		ValidationException exception = Assertions.assertThrows(ValidationException.class,
				() -> validator.validate(request));
		Assertions.assertEquals(Status.INVALID_DATE_SETTING.getCode(), exception.getStatus().getCode());
	}

	@Test
	public void validate() {
		MartRequest request = MartRequest.builder()
				.useCase("feed")
				.ids(List.of("OP01010", "OP01012"))
				.dps(List.of("HP010", "HP022"))
				.startDate("2021-01-01")
				.endDate("2022-01-01")
				.build();
		Assertions.assertDoesNotThrow(() -> validator.validate(request));
	}
}
