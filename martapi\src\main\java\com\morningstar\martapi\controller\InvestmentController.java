package com.morningstar.martapi.controller;

import com.morningstar.martapi.util.InvestmentApiRequestUtil;
import com.morningstar.martapi.validator.RequestValidationHandler;
import com.morningstar.martapi.validator.entity.HeadersAndParams;
import com.morningstar.martgateway.domains.core.entity.investmentresponse.InvestmentResponse;
import com.morningstar.martgateway.interfaces.MartGateway;
import com.morningstar.martgateway.interfaces.model.investmentapi.InvestmentApiRequest;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestHeader;
import reactor.core.publisher.Mono;

@RestController
@RequestMapping(value = {"/v1/data","/investment-api/v1/data"},
        produces = {MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE})
    public class InvestmentController {

    private final MartGateway<InvestmentResponse, InvestmentApiRequest> gridViewGateway;
    private final RequestValidationHandler<HeadersAndParams,InvestmentApiRequest> validator;

    public InvestmentController(
            @Qualifier("gridViewGateway") MartGateway<InvestmentResponse, InvestmentApiRequest> gridViewGateway,
            @Qualifier("investmentApiValidator") RequestValidationHandler<HeadersAndParams,InvestmentApiRequest> validator
    ) {
        this.gridViewGateway = gridViewGateway;
        this.validator = validator;
    }

    @PostMapping(value = "", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Mono<InvestmentResponse> getData(
            @RequestBody InvestmentApiRequest investmentApiRequest,
            @RequestParam(value = "readCache", required = false, defaultValue = "") String readCache,
            @RequestHeader(value = "X-API-UserId", required = false, defaultValue = "") String headerUserId,
            @RequestHeader(value = "Authorization", required = false, defaultValue = "") String token,
            @RequestHeader(value = "X-API-RequestId", required = false, defaultValue = "") String requestId,
            @RequestHeader(value = "X-API-ProductId", required = false, defaultValue = "") String productId,
            @RequestHeader(value = "checkEntitlement", required = false, defaultValue = "true") String checkEntitlement,
            @RequestHeader(value= "X-API-Entitlement-Product", required = false, defaultValue = "") String entitlementProductId,
            @RequestHeader(value = "X-API-SkipProxy", required = false, defaultValue = "false") boolean skipProxy) {
        long startTime = System.currentTimeMillis();
        InvestmentApiRequestUtil investmentApiRequestUtil = new InvestmentApiRequestUtil();
        HeadersAndParams headersAndParams = investmentApiRequestUtil.getHeaderAndParams(token, productId, requestId);

        InvestmentApiRequest validatedInvestmentApiRequest = investmentApiRequestUtil.getValidatedInvestmentApiRequest(investmentApiRequest, token, headerUserId, productId, requestId, readCache, checkEntitlement);
        validatedInvestmentApiRequest.setEntitlementProductId(entitlementProductId);
        validatedInvestmentApiRequest.setSkipProxy(skipProxy);
        validateRequest(investmentApiRequest, headersAndParams);
        return investmentApiRequestUtil.getInvestmentResponse(gridViewGateway, validatedInvestmentApiRequest, headersAndParams, startTime);
    }

    private void validateRequest(InvestmentApiRequest investmentApiRequest, HeadersAndParams headersAndParams) {
        validator.validateHeadersAndParams(headersAndParams);
        validator.validateRequestBody(investmentApiRequest);
    }

}
