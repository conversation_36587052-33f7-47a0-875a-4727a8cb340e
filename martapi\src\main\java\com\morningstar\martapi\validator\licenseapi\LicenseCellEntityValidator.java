package com.morningstar.martapi.validator.licenseapi;

import com.morningstar.martapi.entity.LicenseCellEntity;
import com.morningstar.martapi.exception.ValidationException;
import com.morningstar.martapi.validator.Validator;
import com.morningstar.martgateway.domains.core.entity.response.Status;
import com.mysql.cj.util.StringUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

public class LicenseCellEntityValidator implements Validator<LicenseCellEntity> {
    @Override
    public void validate(LicenseCellEntity content) throws ValidationException {
        if (StringUtils.isNullOrEmpty(content.getDataPointId())) {
            Status status = Status.MISSING_ATTRIBUTE
                    .withMessage("Request input missing mandatory attribute - DataPoint");
            throw new ValidationException(status);
        }
        if (StringUtils.isNullOrEmpty(content.getInvestmentId())) {
            Status status = Status.MISSING_ATTRIBUTE
                    .withMessage("Request input missing mandatory attribute - InvestmentId");
            throw new ValidationException(status);
        }
        if (!StringUtils.isNullOrEmpty(content.getDate()) && !isValidDate(content.getDate())) {
            Status status = Status.INVALID_DATE_SETTING
                    .withMessage("Date is not in correct format: yyyy-MM-dd");
            throw new ValidationException(status);
        }
    }
    private boolean isValidDate(String dateString) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ISO_LOCAL_DATE;
            LocalDate.parse(dateString, formatter);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}
