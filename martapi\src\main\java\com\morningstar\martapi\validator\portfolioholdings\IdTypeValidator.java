package com.morningstar.martapi.validator.portfolioholdings;

import com.morningstar.dataac.martgateway.core.entitlement.entity.InvestmentApiIdType;
import com.morningstar.dataac.martgateway.data.ph.entity.HoldingDataRequest;
import com.morningstar.martapi.exception.HoldingValidationException;
import com.morningstar.martapi.validator.Validator;
import com.morningstar.martgateway.domains.core.entity.response.Status;

public class IdTypeValidator implements Validator<HoldingDataRequest> {

    @Override
    public void validate(HoldingDataRequest request) throws RuntimeException {
        InvestmentApiIdType idType = request.getIdType();
        if (idType == null) {
            throw new HoldingValidationException(Status.INVALID_ID_TYPE);
        }
    }
}
