package com.morningstar.martapi.validator.portfolioholdings;

import static org.junit.Assert.*;

import com.morningstar.dataac.martgateway.core.entitlement.entity.InvestmentApiIdType;
import com.morningstar.martapi.exception.HoldingValidationException;
import com.morningstar.dataac.martgateway.data.ph.entity.HoldingDataRequest;
import org.junit.Test;

public class IdTypeValidatorTest {

    @Test
    public void validate() {
        HoldingDataRequest request = new HoldingDataRequest();
        IdTypeValidator validator = new IdTypeValidator();
        HoldingValidationException exception = assertThrows(
                HoldingValidationException.class, () -> validator.validate(request));
        assertEquals("Invalid idType", exception.getMessage());
        assertEquals("400",exception.getStatus().getCode().substring(0, 3));

        request.setIdType(InvestmentApiIdType.SEC_ID);
        validator.validate(request);
    }
}