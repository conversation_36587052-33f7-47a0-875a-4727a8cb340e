package com.morningstar.martapi.entity;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TranscriptApiResponse {

    @JsonProperty("job_id")
    private String jobId;

    @JsonProperty("event_type")
    private String eventType;

    @JsonProperty("category")
    private String category;

    @JsonProperty("audio_url")
    private String audioUrl;

    @JsonProperty("raw_json_url")
    private String rawJsonUrl;

    @JsonProperty("validated_json_url")
    private String validatedJsonUrl;

    @JsonProperty("raw_pdf_url")
    private String rawPdfUrl;

    @JsonProperty("validated_pdf_url")
    private String validatedPdfUrl;

    @JsonProperty("documents_ids")
    private List<String> documentIds;
}
