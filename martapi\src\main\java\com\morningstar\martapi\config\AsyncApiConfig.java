package com.morningstar.martapi.config;

import com.amazonaws.ClientConfiguration;
import com.amazonaws.services.lambda.AWSLambda;
import com.amazonaws.services.lambda.AWSLambdaClientBuilder;
import com.amazonaws.services.s3.AmazonS3;

import com.morningstar.martapi.service.LambdaService;
import com.morningstar.martapi.service.S3PreSignerProvider;
import com.morningstar.martapi.service.S3Service;
import com.morningstar.martgateway.interfaces.model.AsyncDbDetails;
import java.util.concurrent.Executors;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import reactor.core.scheduler.Scheduler;
import reactor.core.scheduler.Schedulers;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.dynamodb.DynamoDbClient;

@Configuration
public class AsyncApiConfig {

    @Bean
    public S3Service getS3Service(
            @Qualifier("s3Client") AmazonS3 amazonS3,
            @Value("${aws.s3.inputBucket}") String inputBucket,
            @Value("${aws.s3.outputBucket.us}") String usOutputBucket,
            @Value("${aws.s3.outputBucket.eu}") String euOutputBucket,
            @Value("${aws.region}") String region,
            S3PreSignerProvider s3PreSignerProvider) {
        return new S3Service(amazonS3, inputBucket, usOutputBucket, euOutputBucket, region, s3PreSignerProvider);
    }

    @Bean
    public LambdaService getLambdaService(@Value("${aws.region}") String region, @Value("${aws.lambda.function}") String functionName) {
        AWSLambda awsLambda = AWSLambdaClientBuilder.standard()
                .withClientConfiguration(new ClientConfiguration().withMaxErrorRetry(3))
                .withRegion(region).build();
        return new LambdaService(awsLambda, functionName);
    }

    @Bean(name = "asyncScheduler")
    public Scheduler asyncScheduler(@Value("${asyncApi.threadPoolSize:20}") int threadPoolSize) {
        return Schedulers.fromExecutor(Executors.newFixedThreadPool(threadPoolSize));
    }

    @Bean
    public DynamoDbTable<AsyncDbDetails> getDynamoDbTable(@Value("${aws.region}") String region, @Value("${aws.dynamoDb.table}") String table) {
        DynamoDbClient dynamoDbClient = DynamoDbClient.builder()
                .region(Region.of(region))
                .build();

        // Create an Enhanced DynamoDB client
        DynamoDbEnhancedClient enhancedClient = DynamoDbEnhancedClient.builder()
                .dynamoDbClient(dynamoDbClient)
                .build();

        DynamoDbTable<AsyncDbDetails> dynamoDbTable = enhancedClient.table(table, AsyncDbDetails.TABLE_SCHEMA);
        return dynamoDbTable;
    }

}
