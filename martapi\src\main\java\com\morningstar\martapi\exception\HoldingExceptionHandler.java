package com.morningstar.martapi.exception;

import com.morningstar.dataac.martgateway.core.entitlement.exception.EntitlementException;
import com.morningstar.dataac.martgateway.data.ph.exception.PortfolioHoldingException;
import com.morningstar.martapi.controller.PortfolioHoldingsController;
import com.morningstar.martgateway.domains.core.entity.investmentresponse.InvestmentResponse;
import com.morningstar.martgateway.domains.core.entity.response.Status;
import java.util.Map;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.core.codec.DecodingException;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.ServerWebInputException;

import static com.morningstar.martapi.exception.utils.ExceptionHandlerUtils.extractProductId;
import static com.morningstar.martapi.exception.utils.ExceptionHandlerUtils.extractStatusCode;

@Order(Ordered.HIGHEST_PRECEDENCE)
@RestControllerAdvice(assignableTypes = {PortfolioHoldingsController.class})
public class HoldingExceptionHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(HoldingExceptionHandler.class);
    private static final Map<String, String> enumToFieldMap = Map.of(
            "InvestmentApiIdType", "idType",
            "PortfolioDepth", "portfolioDepth",
            "SuppressionTypeEnum", "suppressionType.type",
            "PortfolioDateType", "portfolioDate.type",
            "FixedIncomeEnrichmentType", "fixedIncomeEnrichmentType"
    );
    private static final Pattern ENUM_ERROR_PATTERN = Pattern.compile("JSON decoding error: Cannot deserialize value of type.+\\.(.*)` from String \"(.*)\".*Enum class: \\[(.*)]");
    private static final Pattern EMPTY_STRING_ENUM_PATTERN = Pattern.compile(".+\\.(.*)` value \\(but could if coercion was enabled using `CoercionConfig`\\)");

    @ExceptionHandler(value = ServerWebInputException.class)
    public ResponseEntity<InvestmentResponse> handleServerWebInputException(
            ServerWebInputException e,
            ServerWebExchange ex
    ) {
        String url = ex.getRequest().getURI().toString();
        if (e.getCause() instanceof DecodingException de) {
            return handleDecodingException(de, url);
        }
        return ResponseEntity.badRequest().body(badRequest(e.getMessage()));
    }

    @ExceptionHandler(value = HoldingValidationException.class)
    public ResponseEntity<InvestmentResponse> handleHoldingException(
            HoldingValidationException e,
            ServerWebExchange ex
    ) {
        String url = ex.getRequest().getURI().toString();
        LOGGER.warn("event_type=\"Portfolio Holdings API Validation\", " +
                "event_description=\"Validation failure\", url=\"{}\", e=\"{}\"", url, e.getMessage());
        return ResponseEntity
                .status(extractStatusCode(e.getStatus()))
                .body(new InvestmentResponse(e.getStatus(), null));
    }

    @ExceptionHandler(value = InvestmentApiValidationException.class)
    public ResponseEntity<InvestmentResponse> handleValidationException(
            InvestmentApiValidationException e,
            ServerWebExchange ex
    ) {
        String url = ex.getRequest().getURI().toString();
        LOGGER.warn("event_type=\"API Validation\", " +
                "event_description=\"Validation failure\", url=\"{}\", e=\"{}\"", url, e.getMessage());
        return ResponseEntity
                .status(extractStatusCode(e.getStatus()))
                .body(new InvestmentResponse(e.getStatus(), null));
    }

    @ExceptionHandler(value = PortfolioHoldingException.class)
    public ResponseEntity<InvestmentResponse> handlePortfolioHoldingException(PortfolioHoldingException e,
            ServerWebExchange ex) {
        String url = ex.getRequest().getURI().toString();

        LOGGER.warn("event_type=\"Portfolio Holding\", " +
                "event_description=\"Portfolio holding issue encountered in request\", url=\"{}\", e=\"{}\"", url, e.getMessage(), e);
        return ResponseEntity
                .status(Integer.parseInt(e.getCode()))
                .body(fromStatus(e.getCode(), e.getMessage()));
    }

    @ExceptionHandler(value = EntitlementException.class)
    public ResponseEntity<InvestmentResponse> handleEntitlementException(EntitlementException e, ServerWebExchange ex) {
        String url = ex.getRequest().getURI().toString();
        String productId = extractProductId(ex);
        LOGGER.warn("event_type=\"Portfolio Holding Authorization Failure\", " +
                "event_description=\"Entitlement retrieval failed\", " +
                "url=\"{}\", product_id=\"{}\", e=\"{}\"", url, productId, e.getMessage());
        return ResponseEntity
                .status(extractStatusCode(e.getStatus()))
                .body(new InvestmentResponse(e.getStatus(), null));
    }

    @ExceptionHandler(value = Exception.class)
    public ResponseEntity<InvestmentResponse> handleOtherException(Exception e,
            ServerWebExchange ex) {
        String url = ex.getRequest().getURI().toString();
        LOGGER.warn("event_type=\"Unexpected Exception\", " +
                "event_description=\"Unexpected issue encountered\", url=\"{}\", e=\"{}\"", url, e.getMessage());
        return ResponseEntity.internalServerError()
                .body(internalServerError("Internal Server Error"));
    }

    public String getInvalidEnumErrorMessage(String errorMessage, String url) {
        Matcher matcher = ENUM_ERROR_PATTERN.matcher(errorMessage);
        if (matcher.find()) {
            String enumName = matcher.group(1);
            String requestString = matcher.group(2);
            String validValues = getValidEnumValuesFromErrorMsg(url, matcher, enumName);
            String enumFieldName = enumToFieldMap.getOrDefault(enumName, enumName);
            String description = "Invalid value [%s] for field [%s]. Valid values: [%s]";
            return "Bad Request: " + String.format(description, requestString, enumFieldName, validValues);
        } else {
            return "Bad Request: " + errorMessage;
        }
    }

    private static String getValidEnumValuesFromErrorMsg(String url, Matcher matcher, String enumName) {
        String validValues = matcher.group(3);
        if ("PortfolioDateType".equals(enumName)) {
            if (url.contains("/portfolio-holdings-api/v1/data") || url.contains("/portfolio-holdings-api/v1/stream-data")) {
                // TIME_SERIES is not allowed in this endpoint
                validValues = "mostRecent, staticDates";
            } else {
                validValues = "mostRecent, timeSeries, staticDates";
            }
        }
        if ("InvestmentApiIdType".equalsIgnoreCase(enumName)) {
            validValues = "SecId, MasterPortfolioId";
        }
        return validValues;
    }

    private String getEmptyEnumErrorMessage(String errorMessage) {
        Matcher matcher = EMPTY_STRING_ENUM_PATTERN.matcher(errorMessage);
        if (matcher.find()) {
            String enumName = matcher.group(1);
            String enumFieldName = enumToFieldMap.getOrDefault(enumName, enumName);
            String description = "Mandatory field `%s` is missing or empty";
            return "Bad Request: " + String.format(description, enumFieldName);
        } else {
            return "Bad Request: " + errorMessage;
        }
    }

    private ResponseEntity<InvestmentResponse> handleDecodingException(DecodingException de, String url) {
        String message = Optional.ofNullable(de.getMessage()).orElse("Json decoding error");
        if (message.startsWith("JSON decoding error: Cannot deserialize value of type")) {
            message = getInvalidEnumErrorMessage(de.getMessage(), url);
        } else if (message.startsWith("JSON decoding error: Cannot coerce empty String")) {
            message = getEmptyEnumErrorMessage(de.getMessage());
        } else {
            message = "Invalid request input format";
        }
        LOGGER.warn("event_type=\"Invalid Request Input\", " +
                "event_description=\"Request Input not valid JSON\", url=\"{}\", e=\"{}\"", url, message);
        return ResponseEntity.badRequest().body(new InvestmentResponse(Status.INVALID_REQUEST_INPUT.withMessage(message), null));
    }

    public InvestmentResponse badRequest(String message) {
        Status status = Status.BAD_REQUEST.withMessage(message);
        return new InvestmentResponse(status, null);
    }

    public InvestmentResponse internalServerError(String message) {
        Status status = Status.INTERNAL_ERROR.withMessage(message);
        return new InvestmentResponse(status, null);
    }

    public InvestmentResponse fromStatus(String code, String message) {
        if (code.startsWith("401")) {
            return new InvestmentResponse(Status.UNAUTHORIZED.withMessage(message), null);
        } else if (code.startsWith("400")) {
            return badRequest(message);
        } else {
            return internalServerError(message);
        }
    }
}
