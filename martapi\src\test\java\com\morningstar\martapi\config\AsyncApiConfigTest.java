package com.morningstar.martapi.config;

import static org.mockito.Mockito.mock;

import com.amazonaws.services.s3.AmazonS3;
import com.morningstar.martapi.service.LambdaService;
import com.morningstar.martapi.service.S3PreSignerProvider;
import com.morningstar.martapi.service.S3Service;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import reactor.core.scheduler.Scheduler;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;


@RunWith(MockitoJUnitRunner.class)
public class AsyncApiConfigTest {

    AsyncApiConfig asyncApiConfig = new AsyncApiConfig();

    @Test
    public void getS3Service() {
        AmazonS3 amazonS3 = mock(AmazonS3.class);
        S3Service s3Service = asyncApiConfig.getS3Service(amazonS3, "bucket", "bucket2", "bucket3", "us-east-1",new S3PreSignerProvider());
        Assert.assertNotNull(s3Service);
    }

    @Test
    public void getLambdaService() {
        LambdaService lambdaService = asyncApiConfig.getLambdaService("region", "function");
        Assert.assertNotNull(lambdaService);
    }

    @Test
    public void getAsyncScheduler() {
        Scheduler scheduler = asyncApiConfig.asyncScheduler(1);
        Assert.assertNotNull(scheduler);
    }

    @Test
    public void getDynamoDbTableTest() {
        DynamoDbTable dynamoDbTable = asyncApiConfig.getDynamoDbTable("region", "table");
        Assert.assertNotNull(dynamoDbTable);
    }
}
