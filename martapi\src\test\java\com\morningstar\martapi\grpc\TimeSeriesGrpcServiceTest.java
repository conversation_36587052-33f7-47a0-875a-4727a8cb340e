package com.morningstar.martapi.grpc;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.morningstar.martapi.validator.RequestValidationHandler;
import com.morningstar.martapi.validator.entity.HeadersAndParams;
import com.morningstar.martgateway.domains.core.entity.investmentresponse.Investment;
import com.morningstar.martgateway.domains.core.entity.investmentresponse.InvestmentResponse;
import com.morningstar.martgateway.domains.core.entity.response.Status;
import com.morningstar.martgateway.domains.timeseries.entity.TSContent;
import com.morningstar.martgateway.domains.timeseries.entity.TSData;
import com.morningstar.martgateway.domains.timeseries.entity.TSItem;
import com.morningstar.martgateway.domains.timeseries.entity.TSResponse;
import com.morningstar.martgateway.domains.timeseries.entity.TSStatus;
import com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf;
import com.morningstar.martgateway.interfaces.MartGateway;
import com.morningstar.martcommon.entity.result.request.MartRequest;
import io.grpc.stub.StreamObserver;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import reactor.core.publisher.Mono;

import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class TimeSeriesGrpcServiceTest {

    private TimeSeriesGrpcService timeSeriesGrpcService;

    @Mock
    private MartGateway<TSResponse, MartRequest> tsOldRspGateway;

    @Mock
    private MartGateway<InvestmentResponse, MartRequest> tsNewRspGateway;

    @Mock
    private RequestValidationHandler<HeadersAndParams, MartRequest> validator;

    @Mock
    private StreamObserver<TsCacheDataForProtoBuf.TimeSeriesDatas> timeSeriesDataObserver;

    @Mock
    private StreamObserver<InvestmentResponse> investmentResponseObserver;

    @Before
    public void setup() {
        timeSeriesGrpcService = new TimeSeriesGrpcService(
                tsOldRspGateway,
                tsNewRspGateway,
                validator
        );
    }

    @Test
    public void testGetTimeSeriesData_Success() {
        // Arrange
        TimeSeriesRequest request = createTimeSeriesRequest();
        TSResponse tsResponse = createTSResponse();
        TsCacheDataForProtoBuf.TimeSeriesDatas protobufData = tsResponse.toProtobuf();

        when(tsOldRspGateway.asyncRetrieveSecurities(any(MartRequest.class)))
                .thenReturn(Mono.just(tsResponse));

        // Act
        timeSeriesGrpcService.getTimeSeriesData(request, timeSeriesDataObserver);

        // Assert
        verify(tsOldRspGateway, times(1)).asyncRetrieveSecurities(any(MartRequest.class));
        verify(timeSeriesDataObserver, times(1)).onNext(any(TsCacheDataForProtoBuf.TimeSeriesDatas.class));
        verify(timeSeriesDataObserver, times(1)).onCompleted();
    }

    @Test
    public void testGetTimeSeriesData_Error() {
        // Arrange
        TimeSeriesRequest request = createTimeSeriesRequest();
        RuntimeException exception = new RuntimeException("Gateway error");

        when(tsOldRspGateway.asyncRetrieveSecurities(any(MartRequest.class)))
                .thenReturn(Mono.error(exception));

        // Act
        timeSeriesGrpcService.getTimeSeriesData(request, timeSeriesDataObserver);

        // Assert
        verify(tsOldRspGateway, times(1)).asyncRetrieveSecurities(any(MartRequest.class));
        verify(timeSeriesDataObserver, times(1)).onError(exception);
    }

    @Test
    public void testGetTimeSeriesData_ExceptionInMethod() {
        // Arrange
        TimeSeriesRequest request = createTimeSeriesRequest();
        
        when(tsOldRspGateway.asyncRetrieveSecurities(any(MartRequest.class)))
                .thenThrow(new RuntimeException("Unexpected error"));

        // Act
        timeSeriesGrpcService.getTimeSeriesData(request, timeSeriesDataObserver);

        // Assert
        verify(timeSeriesDataObserver, times(1)).onError(any(RuntimeException.class));
    }

    @Test
    public void testRetrieveTimeSeriesData_Success() {
        // Arrange
        TimeSeriesRequest request = createTimeSeriesRequest();
        TSResponse tsResponse = createTSResponse();

        when(tsOldRspGateway.asyncRetrieveSecurities(any(MartRequest.class)))
                .thenReturn(Mono.just(tsResponse));

        // Act
        timeSeriesGrpcService.retrieveTimeSeriesData(request, timeSeriesDataObserver);

        // Assert
        verify(tsOldRspGateway, times(1)).asyncRetrieveSecurities(any(MartRequest.class));
        verify(timeSeriesDataObserver, times(1)).onNext(any(TsCacheDataForProtoBuf.TimeSeriesDatas.class));
        verify(timeSeriesDataObserver, times(1)).onCompleted();
    }

    @Test
    public void testRetrieveTimeSeriesData_Error() {
        // Arrange
        TimeSeriesRequest request = createTimeSeriesRequest();
        RuntimeException exception = new RuntimeException("Gateway error");

        when(tsOldRspGateway.asyncRetrieveSecurities(any(MartRequest.class)))
                .thenReturn(Mono.error(exception));

        // Act
        timeSeriesGrpcService.retrieveTimeSeriesData(request, timeSeriesDataObserver);

        // Assert
        verify(tsOldRspGateway, times(1)).asyncRetrieveSecurities(any(MartRequest.class));
        verify(timeSeriesDataObserver, times(1)).onError(exception);
    }

    @Test
    public void testGetInvestmentTSData_Success() {
        // Arrange
        TimeSeriesRequest request = createTimeSeriesRequest();
        InvestmentResponse investmentResponse = createInvestmentResponse();

        when(tsNewRspGateway.asyncRetrieveSecurities(any(MartRequest.class)))
                .thenReturn(Mono.just(investmentResponse));

        // Act
        timeSeriesGrpcService.getInvestmentTSData(request, investmentResponseObserver);

        // Assert
        verify(tsNewRspGateway, times(1)).asyncRetrieveSecurities(any(MartRequest.class));
        verify(investmentResponseObserver, times(1)).onNext(any(InvestmentResponse.class));
        verify(investmentResponseObserver, times(1)).onCompleted();
    }

    @Test
    public void testGetInvestmentTSData_Error() {
        // Arrange
        TimeSeriesRequest request = createTimeSeriesRequest();
        RuntimeException exception = new RuntimeException("Gateway error");

        when(tsNewRspGateway.asyncRetrieveSecurities(any(MartRequest.class)))
                .thenReturn(Mono.error(exception));

        // Act
        timeSeriesGrpcService.getInvestmentTSData(request, investmentResponseObserver);

        // Assert
        verify(tsNewRspGateway, times(1)).asyncRetrieveSecurities(any(MartRequest.class));
        verify(investmentResponseObserver, times(1)).onError(exception);
    }

    @Test
    public void testRequestConversion() {
        // Arrange
        TimeSeriesRequest request = createTimeSeriesRequest();

        when(tsOldRspGateway.asyncRetrieveSecurities(any(MartRequest.class)))
                .thenReturn(Mono.just(createTSResponse()));

        ArgumentCaptor<MartRequest> martRequestCaptor = ArgumentCaptor.forClass(MartRequest.class);

        // Act
        timeSeriesGrpcService.getTimeSeriesData(request, timeSeriesDataObserver);

        // Assert
        verify(tsOldRspGateway).asyncRetrieveSecurities(martRequestCaptor.capture());
        MartRequest capturedRequest = martRequestCaptor.getValue();

        assertEquals("F000010IS3", capturedRequest.getIds().get(0));
        assertEquals("HP010", capturedRequest.getDps().get(0));
        assertEquals("2023-01-01", capturedRequest.getStartDate());
        assertEquals("2023-12-31", capturedRequest.getEndDate());
        assertEquals("USD", capturedRequest.getCurrency());
        assertEquals("test-product", capturedRequest.getProductId());
        assertEquals("test-user", capturedRequest.getUserId());
        assertNotNull(capturedRequest.getRequestId());
    }

    @Test
    public void testRequestIdGeneration() {
        // Arrange - request without requestId
        TimeSeriesRequest request = TimeSeriesRequest.newBuilder()
                .addInvestmentIds("F000010IS3")
                .addDataPoints("HP010")
                .setUserId("test-user")
                .setProductId("test-product")
                .build();

        when(tsOldRspGateway.asyncRetrieveSecurities(any(MartRequest.class)))
                .thenReturn(Mono.just(createTSResponse()));

        ArgumentCaptor<MartRequest> martRequestCaptor = ArgumentCaptor.forClass(MartRequest.class);

        // Act
        timeSeriesGrpcService.getTimeSeriesData(request, timeSeriesDataObserver);

        // Assert
        verify(tsOldRspGateway).asyncRetrieveSecurities(martRequestCaptor.capture());
        MartRequest capturedRequest = martRequestCaptor.getValue();

        assertNotNull(capturedRequest.getRequestId());
        assertEquals(36, capturedRequest.getRequestId().length()); // UUID length
    }

    // Helper methods for creating test data
    private TimeSeriesRequest createTimeSeriesRequest() {
        return TimeSeriesRequest.newBuilder()
                .setRequestId("test-request-123")
                .addInvestmentIds("F000010IS3")
                .addDataPoints("HP010")
                .setStartDate("2023-01-01")
                .setEndDate("2023-12-31")
                .setCurrency("USD")
                .setProductId("test-product")
                .setUserId("test-user")
                .setAuthorization("Bearer test-token")
                .build();
    }

    private TSResponse createTSResponse() {
        TSData tsData = new TSData();
        tsData.setDate(49235);
        tsData.setValue(100.0);

        TSItem item = new TSItem();
        item.setSecid("F000010IS3");
        item.setDataid("HP010");
        item.setData(List.of(tsData));

        TSContent content = new TSContent();
        content.setItems(List.of(item));

        TSResponse tsResponse = new TSResponse();
        tsResponse.setStatus(new TSStatus("200", "Success"));
        tsResponse.setContent(content);

        return tsResponse;
    }

    private InvestmentResponse createInvestmentResponse() {
        Investment investment = new Investment("F000010IS3");
        InvestmentResponse investmentResponse = new InvestmentResponse();
        investmentResponse.setStatus(Status.OK);
        investmentResponse.setInvestments(List.of(investment));
        return investmentResponse;
    }

    @Test
    public void testGetTimeSeriesData_WithEmptyRequestId() {
        // Arrange
        TimeSeriesRequest request = TimeSeriesRequest.newBuilder()
                .setRequestId("")
                .addInvestmentIds("F000010IS3")
                .addDataPoints("HP010")
                .setUserId("test-user")
                .build();

        when(tsOldRspGateway.asyncRetrieveSecurities(any(MartRequest.class)))
                .thenReturn(Mono.just(createTSResponse()));

        ArgumentCaptor<MartRequest> martRequestCaptor = ArgumentCaptor.forClass(MartRequest.class);

        // Act
        timeSeriesGrpcService.getTimeSeriesData(request, timeSeriesDataObserver);

        // Assert
        verify(tsOldRspGateway).asyncRetrieveSecurities(martRequestCaptor.capture());
        MartRequest capturedRequest = martRequestCaptor.getValue();

        assertNotNull(capturedRequest.getRequestId());
        assertEquals(36, capturedRequest.getRequestId().length()); // UUID length
    }

    @Test
    public void testGetTimeSeriesData_WithMultipleInvestmentIds() {
        // Arrange
        TimeSeriesRequest request = TimeSeriesRequest.newBuilder()
                .setRequestId("test-request-123")
                .addInvestmentIds("F000010IS3")
                .addInvestmentIds("F000020IS4")
                .addDataPoints("HP010")
                .addDataPoints("HP020")
                .setUserId("test-user")
                .build();

        when(tsOldRspGateway.asyncRetrieveSecurities(any(MartRequest.class)))
                .thenReturn(Mono.just(createTSResponse()));

        ArgumentCaptor<MartRequest> martRequestCaptor = ArgumentCaptor.forClass(MartRequest.class);

        // Act
        timeSeriesGrpcService.getTimeSeriesData(request, timeSeriesDataObserver);

        // Assert
        verify(tsOldRspGateway).asyncRetrieveSecurities(martRequestCaptor.capture());
        MartRequest capturedRequest = martRequestCaptor.getValue();

        assertEquals(2, capturedRequest.getIds().size());
        assertEquals("F000010IS3", capturedRequest.getIds().get(0));
        assertEquals("F000020IS4", capturedRequest.getIds().get(1));
        assertEquals(2, capturedRequest.getDps().size());
        assertEquals("HP010", capturedRequest.getDps().get(0));
        assertEquals("HP020", capturedRequest.getDps().get(1));
    }

    @Test
    public void testGetInvestmentTSData_ExceptionInMethod() {
        // Arrange
        TimeSeriesRequest request = createTimeSeriesRequest();

        when(tsNewRspGateway.asyncRetrieveSecurities(any(MartRequest.class)))
                .thenThrow(new RuntimeException("Unexpected error"));

        // Act
        timeSeriesGrpcService.getInvestmentTSData(request, investmentResponseObserver);

        // Assert
        verify(investmentResponseObserver, times(1)).onError(any(RuntimeException.class));
    }

    @Test
    public void testRetrieveTimeSeriesData_ExceptionInMethod() {
        // Arrange
        TimeSeriesRequest request = createTimeSeriesRequest();

        when(tsOldRspGateway.asyncRetrieveSecurities(any(MartRequest.class)))
                .thenThrow(new RuntimeException("Unexpected error"));

        // Act
        timeSeriesGrpcService.retrieveTimeSeriesData(request, timeSeriesDataObserver);

        // Assert
        verify(timeSeriesDataObserver, times(1)).onError(any(RuntimeException.class));
    }

    @Test
    public void testGetTimeSeriesData_WithAllOptionalFields() {
        // Arrange
        TimeSeriesRequest request = TimeSeriesRequest.newBuilder()
                .setRequestId("test-request-123")
                .addInvestmentIds("F000010IS3")
                .addDataPoints("HP010")
                .setStartDate("2023-01-01")
                .setEndDate("2023-12-31")
                .setCurrency("EUR")
                .setPreCurrency("USD")
                .setReadCache("true")
                .setDateFormat("yyyy-MM-dd")
                .setDecimalFormat("0.00")
                .setExtendPerformance("true")
                .setPostTax("false")
                .setUseRequireId(true)
                .setUseCase("test-case")
                .setUseNewCcs(false)
                .setProductId("test-product")
                .setUserId("test-user")
                .setAuthorization("Bearer test-token")
                .setCheckEntitlement(true)
                .setEntitlementProductId("entitlement-product")
                .build();

        when(tsOldRspGateway.asyncRetrieveSecurities(any(MartRequest.class)))
                .thenReturn(Mono.just(createTSResponse()));

        ArgumentCaptor<MartRequest> martRequestCaptor = ArgumentCaptor.forClass(MartRequest.class);

        // Act
        timeSeriesGrpcService.getTimeSeriesData(request, timeSeriesDataObserver);

        // Assert
        verify(tsOldRspGateway).asyncRetrieveSecurities(martRequestCaptor.capture());
        MartRequest capturedRequest = martRequestCaptor.getValue();

        assertEquals("EUR", capturedRequest.getCurrency());
        assertEquals("USD", capturedRequest.getPreCurrency());
        assertEquals("true", capturedRequest.getReadCache());
        assertEquals("yyyy-MM-dd", capturedRequest.getDateFormat());
        assertEquals("0.00", capturedRequest.getDecimalFormat());
        assertEquals("true", capturedRequest.getExtendedPerformance());
        assertEquals("false", capturedRequest.getPostTax());
        assertEquals(true, capturedRequest.getUseRequireId());
        assertEquals("test-case", capturedRequest.getUseCase());
        assertEquals(false, capturedRequest.isUseNewCCS());
        assertEquals(true, capturedRequest.isCheckEntitlement());
        assertEquals("entitlement-product", capturedRequest.getEntitlementProductId());
    }

    @Test
    public void testStreamObserverCallbacks() {
        // Arrange
        TimeSeriesRequest request = createTimeSeriesRequest();
        TSResponse tsResponse = createTSResponse();

        // Mock StreamObserver to capture callback invocations
        @SuppressWarnings("unchecked")
        StreamObserver<TsCacheDataForProtoBuf.TimeSeriesDatas> mockObserver = mock(StreamObserver.class);

        when(tsOldRspGateway.asyncRetrieveSecurities(any(MartRequest.class)))
                .thenReturn(Mono.just(tsResponse));

        // Act
        timeSeriesGrpcService.getTimeSeriesData(request, mockObserver);

        // Assert - verify the sequence of calls
        verify(mockObserver, times(1)).onNext(any(TsCacheDataForProtoBuf.TimeSeriesDatas.class));
        verify(mockObserver, times(1)).onCompleted();
        verify(mockObserver, times(0)).onError(any(Throwable.class));
    }
}
