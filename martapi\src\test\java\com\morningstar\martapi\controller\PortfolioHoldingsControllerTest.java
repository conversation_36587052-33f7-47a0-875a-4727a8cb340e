package com.morningstar.martapi.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.morningstar.dataac.martgateway.core.entitlement.service.DataEntitlementService;
import com.morningstar.dataac.martgateway.data.ph.entity.HoldingDataPoint;
import com.morningstar.dataac.martgateway.data.ph.entity.HoldingDataRequest;
import com.morningstar.dataac.martgateway.data.ph.entity.PortfolioHoldingDateResponse;
import com.morningstar.dataac.martgateway.data.ph.entity.PortfolioSetting;
import com.morningstar.dataac.martgateway.data.ph.exception.PortfolioHoldingException;
import com.morningstar.dataac.martgateway.data.ph.service.PhDataPointInfo;
import com.morningstar.dataac.martgateway.service.ph.service.gateway.PortfolioHoldingDataGatewayImpl;
import com.morningstar.martapi.config.ValidatorsConfig;
import com.morningstar.martapi.entity.AsyncApiResponseEntity;
import com.morningstar.martapi.exception.InvestmentApiValidationException;
import com.morningstar.martapi.service.AsyncApiService;
import com.morningstar.martapi.validator.portfolioholdings.HoldingDataRequestBuilder;
import com.morningstar.martcommon.entity.datapoint.DataPoint;
import com.morningstar.martgateway.domains.core.entity.response.Status;
import com.morningstar.martgateway.infrastructures.config.ProductIdsRegistry;
import com.morningstar.martgateway.interfaces.MartGateway;
import com.morningstar.martgateway.interfaces.model.investmentapi.Investment;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.Executors;

import org.junit.Assert;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.core.io.buffer.DataBuffer;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Scheduler;
import reactor.core.scheduler.Schedulers;
import reactor.test.StepVerifier;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class PortfolioHoldingsControllerTest {

    private static final String MOCK_TOKEN = "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6Ik1FWXlOakZDTVRVME0wRkdSRGxCUTBVeE56RTFRamt6TWtaR1JUTTJOME01TlVZelJFWTJOdyJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ULSU3VZx76Xxk9tG6qxUlm2x_ABohvUjsK-STjEINb7Ju_RsOMAmCbNL5r2DYlHFjVbSaf0sikNpQCNQuT2x5k3psA4fhxyqy7nP346fogDVFinyINzVBhw2OfO9wzLuuWLWDSM94tV_oStfcfaaYonwpSO_0kD8ey9wOqlfPYKoJbheXQa014qldmIv-EBVWUj0Zd0NsWu4JbWt9SD-dh9lpZMcErM5i58YNgXkKA_O0MvbsVJrtbQpTpiPXbCTuhhY6WTWRUcT7NOSnLGuHSsE5fj6bmZb9ei2DfSmqJRS9Ft2-gDdlmJ-1ZaHEcEjtzEtuEohYmRtQ7Vhljju3w";
    private static final ValidatorsConfig validatorsConfig = new ValidatorsConfig();
    private PortfolioHoldingsController controller;
    @Mock
    private MartGateway<PortfolioHoldingDateResponse, HoldingDataRequest> holdingDateGateway;
    @Mock
    private PortfolioHoldingDataGatewayImpl portfolioHoldingDataGatewayImpl;
    @Mock
    private AsyncApiService asyncApiService;
    @Mock
    private ProductIdsRegistry productIdsRegistry;
    @Mock
    private DataEntitlementService<HoldingDataRequest> dataEntitlementService;

    private final Scheduler asyncScheduler = Schedulers.fromExecutor(Executors.newSingleThreadExecutor());

    @BeforeEach
    public void setUp() {
        when(productIdsRegistry.isEmpty()).thenReturn(false);
        when(productIdsRegistry.hasProductId(anyString())).thenReturn(true);

        DataPoint dp1 = DataPoint.builder().id("id1").build();
        PhDataPointInfo.addBaseLevelPathToDataPointEntry(dp1);

        controller = new PortfolioHoldingsController(
                holdingDateGateway,
                portfolioHoldingDataGatewayImpl,
                asyncApiService,
                dataEntitlementService,
                asyncScheduler,
                validatorsConfig.holdingDataSyncValidator(productIdsRegistry),
                validatorsConfig.holdingDataASyncValidator(productIdsRegistry, dataEntitlementService),
                validatorsConfig.holdingDateValidator(productIdsRegistry));
    }

    @Test
    public void getDates() {
        HoldingDataRequest request = new HoldingDataRequestBuilder().basic();

        when(holdingDateGateway.asyncRetrieveSecurities(any(HoldingDataRequest.class))).thenReturn(Mono.just(new PortfolioHoldingDateResponse()));
        Mono<PortfolioHoldingDateResponse> response = controller.getDates(request, "productId", UUID.randomUUID().toString(), createToken());
        StepVerifier.create(response)
                .expectSubscription()
                .assertNext(r -> {
                    assertEquals(r.getClass(), PortfolioHoldingDateResponse.class);
                });
    }

    @Test
    public void streamDataTest() {
        HoldingDataRequest request = new HoldingDataRequestBuilder().basic();
        DataBuffer dataBuffer = mock(DataBuffer.class);
        when(portfolioHoldingDataGatewayImpl.streamHoldingData(request)).thenReturn(Flux.just(dataBuffer));
        Flux<DataBuffer> dataBufferFlux = controller.streamHoldingData(request, "productId", UUID.randomUUID().toString(), createToken());
        StepVerifier.create(dataBufferFlux)
                .expectSubscription()
                .assertNext(r -> {
                    assertEquals(r.getClass(), DataBuffer.class);
                });
    }

    @Test
    public void getHoldingAsyncDataRequestAndProductIdValidation() {
        ValidatorsConfig validatorsConfig = new ValidatorsConfig();
        validatorsConfig.holdingDataSyncValidator(productIdsRegistry);

        HoldingDataRequest holdingDataRequest = new HoldingDataRequest();
        Set<Investment> investmentsSet = new HashSet<>();
        holdingDataRequest.setInvestments(investmentsSet);

        InvestmentApiValidationException requestIdMissing = assertThrows(InvestmentApiValidationException.class, () ->
                controller.fetchAsyncData(holdingDataRequest, "product1", "", createToken()));
        assertEquals("Request Id is invalid or missing", requestIdMissing.getMessage());
        InvestmentApiValidationException requestIdInvalid = assertThrows(InvestmentApiValidationException.class, () ->
                controller.fetchAsyncData(holdingDataRequest,"product1", "invalid", createToken()));
        assertEquals("Request Id is invalid or missing", requestIdInvalid.getMessage());
        InvestmentApiValidationException productIdMissing = assertThrows(InvestmentApiValidationException.class, () ->
                controller.fetchAsyncData(holdingDataRequest,"", "", createToken()));
        assertEquals("Product Id is invalid or missing", productIdMissing.getMessage());
    }

    @Test
    public void getHoldingDatesRequestAndProductIdValidation() {
        ValidatorsConfig validatorsConfig = new ValidatorsConfig();
        validatorsConfig.holdingDataSyncValidator(productIdsRegistry);

        HoldingDataRequest holdingDataRequest = new HoldingDataRequest();
        Set<Investment> investmentsSet = new HashSet<>();
        holdingDataRequest.setInvestments(investmentsSet);

        InvestmentApiValidationException requestIdMissing = assertThrows(InvestmentApiValidationException.class, () ->
                controller.getDates(holdingDataRequest, "product1", "", createToken()));
        assertEquals("Request Id is invalid or missing", requestIdMissing.getMessage());
        InvestmentApiValidationException requestIdInvalid = assertThrows(InvestmentApiValidationException.class, () ->
                controller.getDates(holdingDataRequest,"product1", "invalid", createToken()));
        assertEquals("Request Id is invalid or missing", requestIdInvalid.getMessage());
        InvestmentApiValidationException productIdMissing = assertThrows(InvestmentApiValidationException.class, () ->
                controller.getDates(holdingDataRequest,"", "", createToken()));
        assertEquals("Product Id is invalid or missing", productIdMissing.getMessage());
    }

    @Test
    public void testFetchAsyncData() {
        AsyncApiResponseEntity mockResponse = AsyncApiResponseEntity.builder().jobId("job1").jobStatus("Submitted").build();
        when(asyncApiService.fetchAsyncData(any())).thenReturn(mockResponse);

        HoldingDataPoint holdingDataPoint = new HoldingDataPoint();
        holdingDataPoint.setDataPointId("HS0100");
        List<HoldingDataPoint> holdingDataPoints = Collections.singletonList(holdingDataPoint);
        PortfolioSetting portfolioSetting = new PortfolioSetting();
        portfolioSetting.setHoldingsDataPoints(holdingDataPoints);

        HoldingDataRequest holdingDataRequest = HoldingDataRequest.builder()
                .useCase("view")
                .investments(Collections.singleton(Investment.builder().id("F0000USA").build()))
                .portfolioSetting(portfolioSetting)
                .build();
        Mono<AsyncApiResponseEntity> response = controller.fetchAsyncData(new HoldingDataRequestBuilder().basic(),"product1", UUID.randomUUID().toString(), createToken());

        StepVerifier.create(response)
                .expectSubscription()
                .assertNext(r -> assertEquals("Submitted", r.getJobStatus()))
                .verifyComplete();
    }

    @Test
    public void testFetchAsyncDataHasError() {
        doThrow(new RuntimeException()).when(asyncApiService).fetchAsyncData(any());

        StepVerifier.create(controller.fetchAsyncData(new HoldingDataRequestBuilder().basic(),"product1", UUID.randomUUID().toString(), createToken()))
                .expectErrorMatches(e -> e instanceof PortfolioHoldingException && ((PortfolioHoldingException) e).getCode().equals(Status.INTERNAL_ERROR.getCode()))
                .verify();
    }

    @Test
    public void testAuthTokenValidator() {
        InvestmentApiValidationException exception = assertThrows(InvestmentApiValidationException.class, () ->
                controller.fetchAsyncData(new HoldingDataRequestBuilder().basic(),"",  "request1", "token")
        );
        assertEquals(Status.INVALID_TOKEN.getMessage(), exception.getStatus().getMessage());
        exception = assertThrows(InvestmentApiValidationException.class, () ->
                controller.fetchAsyncData(new HoldingDataRequestBuilder().basic(),"product1", "request1", MOCK_TOKEN));
        assertEquals(Status.EXPIRED_TOKEN, exception.getStatus());
    }

    @Test
    public void testFetchAsyncDataHasValidationError() {
        doThrow(new PortfolioHoldingException(Status.BAD_REQUEST.getCode(), Status.BAD_REQUEST.getMessage())).when(asyncApiService).fetchAsyncData(any());

        StepVerifier.create(controller.fetchAsyncData(new HoldingDataRequestBuilder().basic(),"product1", UUID.randomUUID().toString(), createToken()))
                .expectErrorMatches(e -> e instanceof PortfolioHoldingException && ((PortfolioHoldingException) e).getCode().equals(Status.BAD_REQUEST.getCode()))
                .verify();
    }

    @Test
    public void testGetAsyncStatusHasError() {
        doThrow(new RuntimeException()).when(asyncApiService).getPHStatus(any(),any(),any(),any(),any());

        StepVerifier.create(controller.getAsyncJobStatus("product1", UUID.randomUUID().toString(), "", createToken(), "job", null))
                .expectErrorMatches(e -> e instanceof PortfolioHoldingException && e.getMessage().equals(Status.INTERNAL_ERROR.getMessage()))
                .verify();
    }

    @Test
    public void testGetAsyncStatus() {
        AsyncApiResponseEntity mockResponse = AsyncApiResponseEntity.builder().jobStatus("Submitted").build();
        when(asyncApiService.getPHStatus(any(),any(),any(),any(),any())).thenReturn(mockResponse);
        Mono<AsyncApiResponseEntity> response = controller.getAsyncJobStatus("product1", UUID.randomUUID().toString(), "userId", createToken(), "job", null);

        StepVerifier.create(response)
                .expectSubscription()
                .assertNext(r -> Assert.assertEquals("Submitted", r.getJobStatus()))
                .verifyComplete();
    }

    @Test
    public void testGetAsyncStatusHasValidationError() {
        doThrow(new InvestmentApiValidationException(Status.BAD_REQUEST)).when(asyncApiService).getPHStatus(any(),any(),any(),any(),any());

        StepVerifier.create(controller.getAsyncJobStatus("product1", UUID.randomUUID().toString(), "userId",createToken(), "job", null))
                .expectErrorMatches(e -> e instanceof InvestmentApiValidationException && e.getMessage().equals(Status.BAD_REQUEST.getMessage()))
                .verify();
    }

    private String createToken() {
        return JWT.create()
                .withExpiresAt(ZonedDateTime.now(ZoneOffset.UTC).plusDays(1).toInstant())
                .withClaim("https://morningstar.com/mstar_id", UUID.randomUUID().toString())
                .sign(Algorithm.none());
    }

}