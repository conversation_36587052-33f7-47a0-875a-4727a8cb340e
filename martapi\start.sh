#!/usr/bin/env bash

export NEW_RELIC_LABELS="BSId:Bus0141;BSName:MDS;TSId:Ts01695;TSName:${SERVICE_TYPE}"

java     -XshowSettings:vm \
         -javaagent:/opt/newrelic/java/newrelic.jar \
         -jar \
         -XX:NewRatio=1 \
         -XX:MaxRAMPercentage=85 \
         -XX:InitialRAMPercentage=85 \
         -XX:+HeapDumpOnOutOfMemoryError \
         -Dspring.profiles.active=$ACTIVE_ENV \
         -Dnewrelic.config.app_name=Bus0141-Ts01695-${SERVICE_TYPE}-${ACTIVE_ENV} \
         -Dnewrelic.config.log_file_name=STDOUT \
         -Dnewrelic.config.license_key=$NEW_RELIC_LICENSE_KEY \
		 -Dreactor.netty.http.server.accessLogEnabled=true \
         martapi.jar