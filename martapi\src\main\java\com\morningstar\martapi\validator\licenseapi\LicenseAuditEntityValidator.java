package com.morningstar.martapi.validator.licenseapi;

import com.morningstar.martapi.entity.LicenseAuditEntity;
import com.morningstar.martapi.exception.ValidationException;
import com.morningstar.martapi.validator.Validator;
import com.morningstar.martgateway.domains.core.entity.response.Status;
import com.mysql.cj.util.StringUtils;
import org.apache.commons.collections4.CollectionUtils;

public class LicenseAuditEntityValidator implements Validator<LicenseAuditEntity> {
    @Override
    public void validate(LicenseAuditEntity content) throws RuntimeException {
        if (CollectionUtils.isEmpty(content.getDatapoints())) {
            Status status = Status.MISSING_ATTRIBUTE
                    .withMessage("Request input missing mandatory attribute - DataPoint list");
            throw new ValidationException(status);
        }
        if (CollectionUtils.isEmpty(content.getInvestments())) {
            Status status = Status.MISSING_ATTRIBUTE
                    .withMessage("Request input missing mandatory attribute - Investment list");
            throw new ValidationException(status);
        }
        if (content.getDatapoints().stream().filter(dp -> StringUtils.isEmptyOrWhitespaceOnly(dp)).findFirst().isPresent()) {
            Status status = Status.INVALID_PARAMETER
                    .withMessage("DataPoint list cannot have an empty datapoint id");
            throw new ValidationException(status);
        }
        if (content.getInvestments().stream().filter(inv -> StringUtils.isEmptyOrWhitespaceOnly(inv)).findFirst().isPresent()) {
            Status status = Status.INVALID_PARAMETER
                    .withMessage("Investment list cannot have an empty investment id");
            throw new ValidationException(status);
        }
    }
}
