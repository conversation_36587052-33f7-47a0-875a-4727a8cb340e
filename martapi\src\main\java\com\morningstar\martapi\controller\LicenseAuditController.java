package com.morningstar.martapi.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.morningstar.dataac.martgateway.core.entitlement.entity.LicenseCellResponse;
import com.morningstar.martapi.entity.LicenseAuditEntity;
import com.morningstar.martapi.entity.LicenseAuditResponse;
import com.morningstar.martapi.entity.LicenseCellEntity;
import com.morningstar.martapi.service.LicenseAuditService;
import com.morningstar.martapi.util.LoggerUtil;
import com.morningstar.martapi.util.TokenUtil;
import com.morningstar.martapi.validator.RequestValidationHandler;
import com.morningstar.martapi.validator.entity.HeadersAndParams;
import com.mysql.cj.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.inject.Inject;
import java.util.UUID;

@RestController
@RequestMapping(value = {"/v1"},
        produces = {MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE})
public class LicenseAuditController {
    private LicenseAuditService licenseAuditService;
    private final RequestValidationHandler<HeadersAndParams,LicenseAuditEntity> licenseAuditValidator;
    private final RequestValidationHandler<HeadersAndParams,LicenseCellEntity> licenseCellValidator;

    private static final Logger log = LoggerFactory.getLogger(LicenseAuditController.class);

    @Inject
    public LicenseAuditController(LicenseAuditService licenseAuditService, @Qualifier("licenseAuditValidator")RequestValidationHandler<HeadersAndParams,LicenseAuditEntity> licenseAuditValidator, @Qualifier("licenseCellValidator")RequestValidationHandler<HeadersAndParams,LicenseCellEntity> licenseCellValidator) {
        this.licenseAuditService = licenseAuditService;
        this.licenseAuditValidator = licenseAuditValidator;
        this.licenseCellValidator = licenseCellValidator;
    }

    /***
     *
     * @param auditRequest
     * @param token
     * @param requestId
     * @return
     * @throws JsonProcessingException
     */
    @PostMapping(value = "license-audit", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> getAudit(
            @RequestBody LicenseAuditEntity auditRequest,
            @RequestHeader(value = "Authorization", required = false, defaultValue = "") String token,
            @RequestHeader(value = "X-API-ProductId", required = false, defaultValue = "") String productId,
            @RequestHeader(value = "X-API-RequestId", required = false, defaultValue = "") String requestId) {
        LoggerUtil loggerUtil = new LoggerUtil();
        long startTime = System.currentTimeMillis();
        String userId = TokenUtil.getUserId(token);
        String configId = TokenUtil.getConfigId(token);
        String request = StringUtils.isEmptyOrWhitespaceOnly(requestId) ? UUID.randomUUID().toString() : requestId;
        try {
           validateLicenseAuditRequest(auditRequest, token, productId);
            MDC.put("request_id", request);
            log.info("event_type=\"License Audit API\", event_description=\"Submit Audit request\", user_id=\"{}\", request_id=\"{}\"", userId, request);
            LicenseAuditResponse response = licenseAuditService.processAudit(userId, productId, configId, auditRequest, false);
            loggerUtil.logAccess(auditRequest, "", request, userId, startTime, false);
            return new ResponseEntity<>(response, HttpStatus.OK);
        } catch(Exception e) {
            loggerUtil.logError(auditRequest, "", request, userId, startTime, false, e);
            throw e;
        }
        finally {
            MDC.clear();
        }
    }

    /***
     *
     * @param auditRequest
     * @param token
     * @param requestId
     * @return
     * @throws JsonProcessingException
     */
    @PostMapping(value = "license-audit-summary", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> getAuditSummary(
            @RequestBody LicenseAuditEntity auditRequest,
            @RequestHeader(value = "Authorization", required = false, defaultValue = "") String token,
            @RequestHeader(value = "X-API-ProductId", required = false, defaultValue = "") String productId,
            @RequestHeader(value = "X-API-RequestId", required = false, defaultValue = "") String requestId) {
        LoggerUtil loggerUtil = new LoggerUtil();
        long startTime = System.currentTimeMillis();
        String userId = TokenUtil.getUserId(token);
        String configId = TokenUtil.getConfigId(token);
        String request = StringUtils.isEmptyOrWhitespaceOnly(requestId) ? UUID.randomUUID().toString() : requestId;
        try {
            validateLicenseAuditRequest(auditRequest, token, productId);
            MDC.put("request_id", request);
            log.info("event_type=\"License Audit API\", event_description=\"Submit Summary request\", user_id=\"{}\", request_id=\"{}\"", userId, request);
            LicenseAuditResponse response = licenseAuditService.processAudit(userId, productId, configId, auditRequest, true);
            loggerUtil.logAccess(auditRequest, "", request, userId, startTime, true);
            return new ResponseEntity<>(response, HttpStatus.OK);
        } catch(Exception e) {
            loggerUtil.logError(auditRequest, "", request, userId, startTime, true, e);
            throw e;
        }
        finally {
            MDC.clear();
        }
    }

    @PostMapping(value = "license-audit-details", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> getCellInspection(
            @RequestBody LicenseCellEntity cellRequest,
            @RequestHeader(value = "Authorization", required = false, defaultValue = "") String token,
            @RequestHeader(value = "X-API-ProductId", required = false, defaultValue = "") String productId,
            @RequestHeader(value = "X-API-RequestId", required = false, defaultValue = "") String requestId) {
        LoggerUtil loggerUtil = new LoggerUtil();
        long startTime = System.currentTimeMillis();
        String userId = TokenUtil.getUserId(token);
        String configId = TokenUtil.getConfigId(token);
        String request = StringUtils.isEmptyOrWhitespaceOnly(requestId) ? UUID.randomUUID().toString() : requestId;
        try {
            validateLicenseCellRequest(cellRequest, token, productId, requestId);
            MDC.put("request_id", request);
            log.info("event_type=\"License Cell Inspection API\", event_description=\"Submit License cell inspection request\", user_id=\"{}\", request_id=\"{}\"", userId, request);
            LicenseCellResponse response = licenseAuditService.processCellInspection(userId, configId, productId, cellRequest);
            loggerUtil.logAccess(cellRequest, productId, request, userId, startTime, true);
            return new ResponseEntity<>(response, HttpStatus.OK);
        } catch(Exception e) {
            loggerUtil.logError(cellRequest, productId, request, userId, startTime, e);
            throw e;
        }
        finally {
            MDC.clear();
        }
    }
    private void validateLicenseAuditRequest(LicenseAuditEntity licenseAuditEntity, String token, String productId) {
        HeadersAndParams headersAndParams = HeadersAndParams.builder().authorizationToken(token)
                .productId(productId)
                .build();
        licenseAuditValidator.validateHeadersAndParams(headersAndParams);
        licenseAuditValidator.validateRequestBody(licenseAuditEntity);
    }
    private void validateLicenseCellRequest(LicenseCellEntity licenseCellEntity, String token, String productId, String requestId) {
        HeadersAndParams headersAndParams = HeadersAndParams.builder().authorizationToken(token)
                .productId(productId)
                .requestId(requestId)
                .build();
        licenseCellValidator.validateHeadersAndParams(headersAndParams);
        licenseCellValidator.validateRequestBody(licenseCellEntity);
    }
}
