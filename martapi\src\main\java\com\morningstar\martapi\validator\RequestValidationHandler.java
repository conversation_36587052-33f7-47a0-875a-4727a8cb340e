package com.morningstar.martapi.validator;

import com.morningstar.dataac.martgateway.core.entitlement.exception.EntitlementException;
import com.morningstar.dataac.martgateway.core.entitlement.service.DataEntitlementService;
import org.apache.commons.collections4.ListUtils;

import java.util.List;

public class RequestValidationHandler<T,U> {

    private final List<Validator<T>> headerAndParamValidators;
    private final List<Validator<U>> requestBodyValidators;
    private final DataEntitlementService<U> dataEntitlementService;

    public RequestValidationHandler(List<Validator<T>> headerAndParamValidators, List<Validator<U>> requestBodyValidators) {
        this.headerAndParamValidators = ListUtils.emptyIfNull(headerAndParamValidators);
        this.requestBodyValidators = ListUtils.emptyIfNull(requestBodyValidators);
        this.dataEntitlementService = null;
    }

    public RequestValidationHandler(List<Validator<T>> headerAndParamValidators, List<Validator<U>> requestBodyValidators, DataEntitlementService<U> dataEntitlementService) {
        this.headerAndParamValidators = ListUtils.emptyIfNull(headerAndParamValidators);
        this.requestBodyValidators = ListUtils.emptyIfNull(requestBodyValidators);
        this.dataEntitlementService = dataEntitlementService;
    }

    public void validateHeadersAndParams(T t) throws RuntimeException {
        this.headerAndParamValidators.forEach(v -> v.validate(t));
    }

    public void validateRequestBody(U u) throws RuntimeException {
        this.requestBodyValidators.forEach(v -> v.validate(u));
    }

    public void validateDataEntitlement(String userId, String configId) throws EntitlementException {
        this.dataEntitlementService.getEntitlement(userId, configId);
    }
}
