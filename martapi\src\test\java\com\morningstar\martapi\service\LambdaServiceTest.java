package com.morningstar.martapi.service;

import com.amazonaws.ResponseMetadata;
import com.amazonaws.services.lambda.AWSLambda;
import com.amazonaws.services.lambda.model.InvokeResult;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.morningstar.martgateway.domains.core.entity.MartException;
import com.morningstar.martgateway.interfaces.model.AsyncInput.ApiType;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class LambdaServiceTest {
    @Mock
    AWSLambda awsLambda;

    LambdaService lambdaService;

    @Before
    public void setup() {
        lambdaService = new LambdaService(awsLambda, "function");
    }

    @Test
    public void testInvoke() {
        InvokeResult mockResult = new InvokeResult();
        mockResult.setStatusCode(200);
        ResponseMetadata metadata = new ResponseMetadata(Map.of("AWS_REQUEST_ID", "123"));
        mockResult.setSdkResponseMetadata(metadata);
        when(awsLambda.invoke(any())).thenReturn(mockResult);
        lambdaService.invokeLambda("job1", ApiType.INVESTMENT_API);
        Mockito.verify(awsLambda, Mockito.times(1)).invoke(any());
    }

    @Test
    public void testInvokeError() {
        InvokeResult mockResult = new InvokeResult();
        mockResult.setStatusCode(200);
        ResponseMetadata metadata = new ResponseMetadata(Map.of("AWS_REQUEST_ID", "123"));
        mockResult.setSdkResponseMetadata(metadata);
        when(awsLambda.invoke(any())).thenReturn(mockResult);
        lambdaService.invokeLambda("job1", ApiType.INVESTMENT_API);
        Mockito.verify(awsLambda, Mockito.times(1)).invoke(any());
    }

    @Test(expected = MartException.class)
    public void testToJson() throws JsonProcessingException {
        ObjectMapper objectMapper = mock(ObjectMapper.class);
        when(objectMapper.writeValueAsString(any())).thenThrow(JsonProcessingException.class);
        LambdaService lambdaService = new LambdaService(awsLambda, "function", objectMapper);
        lambdaService.invokeLambda("job1", ApiType.INVESTMENT_API);
    }

}
