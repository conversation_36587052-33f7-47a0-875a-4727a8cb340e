package com.morningstar.martapi.controller;

import com.morningstar.martapi.service.RedisMessagePublisher;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;

@RunWith(MockitoJUnitRunner.class)
public class DataPointUpdateControllerTest {

    @Mock
    private RedisMessagePublisher redisMessagePublisher;
    private DataPointUpdateController controller;

    @Before
    public void setUp(){
        controller = new DataPointUpdateController(redisMessagePublisher);
    }

    @Test
    public void publish() {
        controller.publish();
        verify(redisMessagePublisher).publishSync(any(String.class));
    }
}
