package com.morningstar.martapi.validator.portfolioholdings;

import com.morningstar.dataac.martgateway.data.ph.entity.PortfolioDate;
import com.morningstar.dataac.martgateway.data.ph.entity.enums.PortfolioDateType;
import com.morningstar.martapi.exception.HoldingValidationException;
import com.morningstar.dataac.martgateway.data.ph.entity.HoldingDataRequest;
import com.morningstar.dataac.martgateway.data.ph.entity.PortfolioSetting;
import org.junit.Test;

import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.morningstar.dataac.martgateway.data.ph.entity.enums.PortfolioDateType.TIME_SERIES;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertThrows;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;

public class StaticDateCountValidatorTest {

    private final PortfolioSettingsValidator portfolioSettingsValidator = new PortfolioSettingsValidator();

    @Test
    public void testValidate_StaticDates_MultipleStaticDates() {
        // Setup
        HoldingDataRequest request = new HoldingDataRequest();
        PortfolioSetting portfolioSetting = new PortfolioSetting();
        PortfolioDate portfolioDate = new PortfolioDate();
        portfolioDate.setType(PortfolioDateType.STATIC_DATES);
        portfolioDate.setStaticDates(Set.of("2021-01-01", "2021-02-01")); // More than one date
        portfolioSetting.setPortfolioDate(portfolioDate);
        request.setPortfolioSetting(portfolioSetting);

        StaticDateCountValidator validator = new StaticDateCountValidator(1);

        // Execute and Assert
        HoldingValidationException exception = assertThrows(
                HoldingValidationException.class,
                () -> validator.validate(request)
        );

        assertEquals("Cannot have more than 1 `dates` for type `staticDates`", exception.getMessage());
        assertEquals("400", exception.getStatus().getCode().substring(0, 3));
    }

    @Test
    public void testValidate_StaticDates_OneStaticDate() {
        // Setup
        HoldingDataRequest request = new HoldingDataRequest();
        PortfolioSetting portfolioSetting = new PortfolioSetting();
        PortfolioDate portfolioDate = new PortfolioDate();
        portfolioDate.setType(PortfolioDateType.STATIC_DATES);
        portfolioDate.setStaticDates(Set.of("2021-01-01")); // Exactly one date
        portfolioSetting.setPortfolioDate(portfolioDate);
        request.setPortfolioSetting(portfolioSetting);

        StaticDateCountValidator validator = new StaticDateCountValidator(1);

        // Execute and Assert
        assertDoesNotThrow(() -> validator.validate(request));
    }

    @Test
    public void testValidate_NonStaticDates() {
        // Setup
        HoldingDataRequest request = new HoldingDataRequest();
        PortfolioSetting portfolioSetting = new PortfolioSetting();
        PortfolioDate portfolioDate = new PortfolioDate();
        portfolioDate.setType(TIME_SERIES);
        portfolioSetting.setPortfolioDate(portfolioDate);
        request.setPortfolioSetting(portfolioSetting);

        StaticDateCountValidator validator = new StaticDateCountValidator(1);

        // Execute and Assert
        assertDoesNotThrow(() -> validator.validate(request));
    }

    @Test
    public void testValidate_StaticDates_HoldingDate() {
        // Setup
        HoldingDataRequest request = new HoldingDataRequest();
        PortfolioSetting portfolioSetting = new PortfolioSetting();
        PortfolioDate portfolioDate = new PortfolioDate();
        portfolioDate.setType(PortfolioDateType.STATIC_DATES);
        Set<String> staticDates = IntStream.range(0, 21)
                .mapToObj(i -> "2024-12-" + String.format("%02d", i + 1)) // more than 20 dates
                .collect(Collectors.toSet());
        portfolioDate.setStaticDates(staticDates);
        portfolioSetting.setPortfolioDate(portfolioDate);
        request.setPortfolioSetting(portfolioSetting);

        StaticDateCountValidator validator = new StaticDateCountValidator(20);

        // Execute and Assert
        HoldingValidationException exception = assertThrows(
                HoldingValidationException.class,
                () -> validator.validate(request)
        );

        assertEquals("Cannot have more than 20 `dates` for type `staticDates`", exception.getMessage());
        assertEquals("400", exception.getStatus().getCode().substring(0, 3));
    }
}