package com.morningstar.martapi.controller;

import com.morningstar.dataac.martgateway.core.entitlement.entity.CachedEntitlement;
import com.morningstar.dataac.martgateway.core.entitlement.service.DataEntitlementService;
import com.morningstar.dataac.martgateway.data.ph.entity.HoldingDataAsyncInput;
import com.morningstar.dataac.martgateway.data.ph.entity.HoldingDataRequest;
import com.morningstar.dataac.martgateway.data.ph.entity.PortfolioHoldingDateResponse;
import com.morningstar.dataac.martgateway.data.ph.exception.PortfolioHoldingException;
import com.morningstar.dataac.martgateway.service.ph.service.gateway.PortfolioHoldingDataGatewayImpl;
import com.morningstar.martapi.entity.AsyncApiResponseEntity;
import com.morningstar.martapi.exception.InvestmentApiValidationException;
import com.morningstar.martapi.service.AsyncApiService;
import com.morningstar.martapi.util.LoggerUtil;
import com.morningstar.martapi.util.TokenUtil;
import com.morningstar.martapi.validator.RequestValidationHandler;
import com.morningstar.martapi.validator.entity.HeadersAndParams;
import com.morningstar.martgateway.domains.core.entity.response.Status;
import com.morningstar.martgateway.infrastructures.log.LogHelper;
import com.morningstar.martgateway.interfaces.MartGateway;
import com.morningstar.martgateway.interfaces.model.AsyncInput;
import javax.inject.Inject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Scheduler;

import java.util.List;

@RestController
@RequestMapping(value = "/portfolio-holdings-api/v1/", produces = MediaType.APPLICATION_JSON_VALUE)
@Slf4j
public class PortfolioHoldingsController {

    private final MartGateway<PortfolioHoldingDateResponse, HoldingDataRequest> holdingDateGateway;
    private final PortfolioHoldingDataGatewayImpl portfolioHoldingDataGatewayImpl;
    private final AsyncApiService asyncApiService;
    private final DataEntitlementService<HoldingDataRequest> dataEntitlementService;
    private final Scheduler asyncScheduler;
    private final RequestValidationHandler<HeadersAndParams, HoldingDataRequest> syncValidator;
    private final RequestValidationHandler<HeadersAndParams, HoldingDataRequest> asyncValidator;
    private final RequestValidationHandler<HeadersAndParams, HoldingDataRequest> dateValidator;

    @Inject
    public PortfolioHoldingsController(
            MartGateway<PortfolioHoldingDateResponse, HoldingDataRequest> holdingDateGateway,
            PortfolioHoldingDataGatewayImpl portfolioHoldingDataGatewayImpl,
            AsyncApiService asyncApiService,
            DataEntitlementService<HoldingDataRequest> dataEntitlementService,
            @Qualifier("asyncScheduler") Scheduler asyncScheduler,
            @Qualifier("holdingDataValidatorSync") RequestValidationHandler<HeadersAndParams, HoldingDataRequest> syncValidator,
            @Qualifier("holdingDataValidatorAsync") RequestValidationHandler<HeadersAndParams, HoldingDataRequest> asyncValidator,
            @Qualifier("holdingDateValidator") RequestValidationHandler<HeadersAndParams, HoldingDataRequest> dateValidator) {
        this.portfolioHoldingDataGatewayImpl = portfolioHoldingDataGatewayImpl;
        this.holdingDateGateway = holdingDateGateway;
        this.asyncApiService = asyncApiService;
        this.dataEntitlementService = dataEntitlementService;
        this.asyncScheduler = asyncScheduler;
        this.syncValidator = syncValidator;
        this.asyncValidator = asyncValidator;
        this.dateValidator = dateValidator;
    }

    @PostMapping(value = "data", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Flux<DataBuffer> streamHoldingData(
            @RequestBody HoldingDataRequest holdingDataRequest,
            @RequestHeader(value = "X-Api-ProductId", required = false, defaultValue = "") String productId,
            @RequestHeader(value = "X-API-RequestId", required = false, defaultValue = "") String requestId,
            @RequestHeader(value = "Authorization", required = false, defaultValue = "") String token)
    {
        long startTime = System.currentTimeMillis();
        HeadersAndParams headersAndParams = buildHeadersAndParams(token, productId, requestId);

        String userIdFromToken = TokenUtil.getUserId(token);
        String configId = TokenUtil.getConfigId(token);
        CachedEntitlement entitlement = getCachedEntitlement(userIdFromToken, configId);

        holdingDataRequest.setUserId(userIdFromToken);
        holdingDataRequest.setConfigId(configId);
        holdingDataRequest.setRequestId(requestId);
        holdingDataRequest.setCachedEntitlement(entitlement);

        validateRequest(syncValidator, holdingDataRequest, headersAndParams);

        LoggerUtil loggerUtil = new LoggerUtil();
        return portfolioHoldingDataGatewayImpl.streamHoldingData(holdingDataRequest)
                .doOnComplete(() -> loggerUtil.logAccess(holdingDataRequest, headersAndParams, "", startTime))
                .doOnError(e -> loggerUtil.logError(holdingDataRequest, headersAndParams, "", startTime, e));
    }

    @PostMapping(value = "dates", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Mono<PortfolioHoldingDateResponse> getDates(
            @RequestBody HoldingDataRequest holdingDateRequest,
            @RequestHeader(value = "X-Api-ProductId", required = false, defaultValue = "") String productId,
            @RequestHeader(value = "X-Api-RequestId", required = false, defaultValue = "") String requestId,
            @RequestHeader(value = "Authorization", required = false, defaultValue = "") String token)
    {
        HeadersAndParams headersAndParams = buildHeadersAndParams(token, productId, requestId);
        String userIdFromToken = TokenUtil.getUserId(token);
        String configId = TokenUtil.getConfigId(token);
        CachedEntitlement entitlement = getCachedEntitlement(userIdFromToken, configId);

        holdingDateRequest.setUserId(userIdFromToken);
        holdingDateRequest.setConfigId(configId);
        holdingDateRequest.setCachedEntitlement(entitlement);

        validateRequest(dateValidator, holdingDateRequest, headersAndParams);

        long startTime = System.currentTimeMillis();
        LoggerUtil loggerUtil = new LoggerUtil();
        return holdingDateGateway.asyncRetrieveSecurities(holdingDateRequest)
                .doOnEach(LogHelper.logOnNext(list -> loggerUtil.logAccess(holdingDateRequest, headersAndParams, "", startTime)))
                .doOnEach(LogHelper.logOnError(e -> loggerUtil.logError(holdingDateRequest, headersAndParams, "", startTime, e)));
    }

    @PostMapping(value = "async-data", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Mono<AsyncApiResponseEntity> fetchAsyncData(
            @RequestBody HoldingDataRequest holdingDataRequest,
            @RequestHeader(value = "X-Api-ProductId", required = false, defaultValue = "") String productId,
            @RequestHeader(value = "X-Api-RequestId", required = false, defaultValue = "") String requestId,
            @RequestHeader(value = "Authorization", required = false, defaultValue = "") String token
    ) {
        HeadersAndParams headersAndParams = buildHeadersAndParams(token, productId, requestId);

        String userIdFromToken = TokenUtil.getUserId(token);
        String configId = TokenUtil.getConfigId(token);

        CachedEntitlement entitlement = getCachedEntitlement(userIdFromToken, configId);
        holdingDataRequest.setConfigId(configId);
        holdingDataRequest.setCachedEntitlement(entitlement);

        validateRequest(asyncValidator, holdingDataRequest, headersAndParams);

        AsyncInput asyncInput = HoldingDataAsyncInput.builder()
                .productId(productId)
                .userId(userIdFromToken)
                .configId(configId)
                .holdingDataRequest(holdingDataRequest)
                .apiType(AsyncInput.ApiType.HOLDING_API)
                .build();

        return Mono.fromCallable(() -> asyncApiService.fetchAsyncData(asyncInput))
                .subscribeOn(asyncScheduler)
                .doOnError(e -> {
                    log.info("event_type=\"Holding Async Api error\", event_description=\"Holding Async Api fetch Async Data failed\", request_id=\"{}\", error_message=\"{}\"", requestId, e.getMessage());
                    if (e instanceof PortfolioHoldingException e1)
                        throw e1;
                    else
                        throw new PortfolioHoldingException(Status.INTERNAL_ERROR.getCode(), Status.INTERNAL_ERROR.getMessage());
                });
    }

    @GetMapping(value="/async-status/{jobId}")
    public Mono<AsyncApiResponseEntity> getAsyncJobStatus(
            @RequestHeader(value = "X-Api-ProductId", required = false, defaultValue = "") String productId,
            @RequestHeader(value = "X-Api-RequestId", required = false, defaultValue = "") String requestId,
            @RequestHeader(value = "X-Api-UserId", required = false, defaultValue = "") String userId,
            @RequestHeader(value = "Authorization", required = false, defaultValue = "") String bearerToken,
            @PathVariable("jobId") String jobId,
            @RequestParam(value = "pages", required = false) List<Integer> pages
    ){
        HeadersAndParams headersAndParams = HeadersAndParams.builder()
                .authorizationToken(bearerToken)
                .productId(productId)
                .requestId(requestId)
                .build();
        asyncValidator.validateHeadersAndParams(headersAndParams);
        return  Mono.fromCallable(() -> asyncApiService.getPHStatus(productId, bearerToken, jobId.toLowerCase(), userId, pages))
                .subscribeOn(asyncScheduler)
                .doOnError(e -> {
                    if (e instanceof InvestmentApiValidationException e1)
                        throw e1;
                    else
                        throw new PortfolioHoldingException(Status.INTERNAL_ERROR.getCode(), Status.INTERNAL_ERROR.getMessage());
                });
    }

    private HeadersAndParams buildHeadersAndParams(String token, String productId, String requestId) {
        return HeadersAndParams.builder()
                .authorizationToken(token)
                .productId(productId)
                .requestId(requestId)
                .build();
    }

    private void validateRequest(RequestValidationHandler<HeadersAndParams, HoldingDataRequest> validator, HoldingDataRequest holdingDataRequest, HeadersAndParams headersAndParams) {
        validator.validateHeadersAndParams(headersAndParams);
        validator.validateRequestBody(holdingDataRequest);
    }

    private CachedEntitlement getCachedEntitlement(String userId, String configId) {
        try {
            return dataEntitlementService.getEntitlement(userId, configId);
        } catch (Exception e) {
            throw new PortfolioHoldingException(Status.INTERNAL_ERROR.getCode(), "Error while fetching user entitlement");
        }
    }
}