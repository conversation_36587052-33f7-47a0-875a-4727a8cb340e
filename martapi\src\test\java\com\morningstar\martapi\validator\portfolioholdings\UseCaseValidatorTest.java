package com.morningstar.martapi.validator.portfolioholdings;

import com.morningstar.martapi.exception.HoldingValidationException;
import com.morningstar.martapi.validator.investmentapi.UseCaseValidator;
import com.morningstar.dataac.martgateway.data.ph.entity.HoldingDataRequest;
import org.junit.Test;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertThrows;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;

public class UseCaseValidatorTest {

    @Test
    public void testValidate_MissingUseCase() {
        HoldingDataRequest request = new HoldingDataRequest();
        UseCaseValidator validator = new UseCaseValidator<>(HoldingValidationException::new);

        HoldingValidationException exception = assertThrows(
                HoldingValidationException.class,
                () -> validator.validate(request)
        );

        assertEquals("useCase value is invalid", exception.getMessage());
    }

    @Test
    public void testValidate_EmptyUseCase() {
        HoldingDataRequest request = new HoldingDataRequest();
        request.setUseCase("");
        UseCaseValidator validator = new UseCaseValidator<>(HoldingValidationException::new);

        HoldingValidationException exception = assertThrows(
                HoldingValidationException.class,
                () -> validator.validate(request)
        );

        assertEquals("useCase value is invalid", exception.getMessage());
    }

    @Test
    public void testValidate_InvalidUseCase() {
        HoldingDataRequest request = new HoldingDataRequest();
        request.setUseCase("invalidUseCase");
        UseCaseValidator validator = new UseCaseValidator<>(HoldingValidationException::new);

        HoldingValidationException exception = assertThrows(
                HoldingValidationException.class,
                () -> validator.validate(request)
        );

        assertEquals("useCase value is invalid", exception.getMessage());
    }

    @Test
    public void testValidate_ValidUseCase() {
        HoldingDataRequest request = new HoldingDataRequest();
        request.setUseCase("feed");
        UseCaseValidator validator = new UseCaseValidator<>(HoldingValidationException::new);

        assertDoesNotThrow(() -> validator.validate(request));
    }

}