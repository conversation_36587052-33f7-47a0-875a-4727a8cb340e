package com.morningstar.martapi.filter;

import static com.morningstar.dataac.martgateway.core.entitlement.entity.constants.EntitlementConstants.AUTHORIZATION_CONTEXT_KEY;
import static com.morningstar.dataac.martgateway.core.entitlement.entity.constants.EntitlementConstants.CONTEXT_MAP;
import static com.morningstar.dataac.martgateway.core.entitlement.entity.constants.EntitlementConstants.REQUEST_ID_HEADER;

import java.util.List;
import java.util.Optional;
import org.springframework.http.HttpHeaders;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilter;
import org.springframework.web.server.WebFilterChain;
import reactor.core.publisher.Mono;
import reactor.util.context.Context;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

@Component
public class WebHeaderFilter implements WebFilter {

    @Override
    @NonNull
    public Mono<Void> filter(
            @NonNull ServerWebExchange ex,
            @NonNull WebFilterChain chain) {

        ServerWebExchange mutatedExchange = ensureRequestIdIsInRequest(ex);
        mutatedExchange.getResponse().beforeCommit(
                () -> addContextToHttpResponseHeaders(mutatedExchange.getResponse())
        );

        return chain.filter(mutatedExchange).contextWrite(
                        ctx -> addRequestHeadersToContext(mutatedExchange.getRequest(), ctx)
                );
    }

    private static ServerWebExchange ensureRequestIdIsInRequest(ServerWebExchange ex) {
        HttpHeaders headers = ex.getRequest().getHeaders();
        if (headers == null || headers.isEmpty()) {
            return ex;
        }
        Optional<List<String>> requestHeaderMaybe = Optional.ofNullable(ex.getRequest().getHeaders().get(REQUEST_ID_HEADER));
        String requestId = requestHeaderMaybe.isEmpty() ? UUID.randomUUID().toString() : requestHeaderMaybe.get().get(0);
        ServerHttpRequest mutatedRequest = ex.getRequest()
                .mutate()
                .header(REQUEST_ID_HEADER, requestId)
                .build();
        return ex.mutate()
                .request(mutatedRequest)
                .build();
    }

    private static final String MDC_HEADER_PREFIX = "x-api-"; //lowercased because tomcat automatically changes request headers to lowercase.
    private static final String AUTHORIZATION_HEADER = "authorization";

    protected Context addRequestHeadersToContext(
            final ServerHttpRequest request,
            final Context context) {

        final Map<String, String> contextMap = request
                .getHeaders().toSingleValueMap().entrySet()
                .stream()
                .filter(x -> x.getKey().startsWith(MDC_HEADER_PREFIX))
                .collect(Collectors.toMap(v -> v.getKey(),
                        Map.Entry::getValue));
        contextMap.put("Date", LocalDateTime.now(ZoneOffset.UTC)
                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'+00:00'")));
        String authorizationValue = request.getHeaders().getFirst(AUTHORIZATION_HEADER);
        if(authorizationValue != null){
            contextMap.put(AUTHORIZATION_CONTEXT_KEY, authorizationValue);
        }
        return context.put(CONTEXT_MAP, contextMap);
    }

    protected Mono<Void> addContextToHttpResponseHeaders(
            final ServerHttpResponse res) {
        return Mono.deferContextual(ctx -> {
            if (ctx.hasKey(CONTEXT_MAP)){
                final HttpHeaders headers = res.getHeaders();
                ctx.<Map<String, String>>get(CONTEXT_MAP)
                        .forEach(
                                (key, value) -> {
                                    if(key.startsWith(MDC_HEADER_PREFIX)) {
                                        headers.add(key, value);
                                    }
                                }
                        );
            }
            return Mono.empty();
        }).then();
    }
}
