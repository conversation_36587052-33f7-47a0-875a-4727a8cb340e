# New gRPC Methods Documentation

This document describes the newly added gRPC methods to the TimeSeriesService.

## Overview

Two new gRPC methods have been added to support the existing REST endpoints:

1. **`retrieveTimeSeriesData`** - Equivalent to the REST endpoint that returns `TSResponse` (format=1)
2. **`getInvestmentTSData`** - Equivalent to the REST endpoint that returns `InvestmentResponse` (format=2)

## Service Definition

The gRPC service now includes three methods:

```protobuf
service TimeSeriesService {
    // Original method (already implemented)
    rpc GetTimeSeriesData(TimeSeriesRequest) returns (TimeSeriesDatas);
    
    // New methods
    rpc RetrieveTimeSeriesData(TimeSeriesRequest) returns (TimeSeriesDatas);
    rpc GetInvestmentTSData(TimeSeriesRequest) returns (InvestmentResponseProto);
}
```

## New Protobuf Messages

### InvestmentResponseProto
```protobuf
message InvestmentResponseProto {
    StatusProto status = 1;
    repeated InvestmentProto investments = 2;
}
```

### StatusProto
```protobuf
message StatusProto {
    string code = 1;
    string message = 2;
}
```

### InvestmentProto
```protobuf
message InvestmentProto {
    string id = 1;
    repeated ValuePair values = 2;
    repeated string errors = 3;
}
```

### ValuePair
```protobuf
message ValuePair {
    string key = 1;
    string value = 2;
    string date = 3;
    string code = 4;
}
```

## Method Details

### 1. retrieveTimeSeriesData

**Purpose**: Returns time series data in the same format as the original `GetTimeSeriesData` method but uses the TSResponse gateway (format=1).

**Request**: `TimeSeriesRequest` (same as existing method)
**Response**: `TimeSeriesDatas` (same as existing method)

**Usage Example with grpcurl**:
```bash
grpcurl -plaintext -d '{
    "requestId": "test-123",
    "investmentIds": ["FOUSA00001"],
    "dataPoints": ["DP001"],
    "userId": "test-user"
}' localhost:9090 com.morningstar.martapi.grpc.TimeSeriesService/RetrieveTimeSeriesData
```

### 2. getInvestmentTSData

**Purpose**: Returns investment time series data in a new format optimized for investment-specific data.

**Request**: `TimeSeriesRequest` (same as existing method)
**Response**: `InvestmentResponseProto` (new format)

**Usage Example with grpcurl**:
```bash
grpcurl -plaintext -d '{
    "requestId": "test-123",
    "investmentIds": ["FOUSA00001"],
    "dataPoints": ["DP001"],
    "userId": "test-user"
}' localhost:9090 com.morningstar.martapi.grpc.TimeSeriesService/GetInvestmentTSData
```

## Key Differences

| Method | Gateway Used | Response Format | Use Case |
|--------|-------------|----------------|----------|
| `GetTimeSeriesData` | tsOldRspGateway | TimeSeriesDatas | Original protobuf format |
| `RetrieveTimeSeriesData` | tsOldRspGateway | TimeSeriesDatas | Same as original, alternative endpoint |
| `GetInvestmentTSData` | tsNewRspGateway | InvestmentResponseProto | Investment-focused data structure |

## Implementation Notes

1. **Error Handling**: All methods include proper error handling and logging
2. **JWT Processing**: User authentication and configuration extraction from JWT tokens
3. **Reactive Programming**: Uses Spring WebFlux reactive patterns with Mono/Flux
4. **Conversion Logic**: Automatic conversion between Java objects and protobuf messages
5. **Logging**: Comprehensive request/response logging with execution time tracking

## Testing

The implementation includes unit tests that verify:
- Protobuf message generation
- gRPC service method availability
- Compilation success

Run tests with:
```bash
mvn test -Dtest=TimeSeriesGrpcServiceNewMethodsTest
```

## Server Configuration

The gRPC server runs on port 9090 by default. Make sure the following properties are configured:

```properties
grpc.server.port=9090
grpc.server.reflection-service-enabled=true
```

## Client Usage

Clients can connect using any gRPC client library. The service supports:
- Plaintext connections (for development)
- TLS connections (for production)
- Server reflection for service discovery

Example with Postman:
1. Create new gRPC request
2. Server URL: `localhost:9090`
3. Use server reflection to discover services
4. Select the desired method and provide request data
