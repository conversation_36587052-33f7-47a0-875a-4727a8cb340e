package com.morningstar.martapi.config;

import com.morningstar.martapi.service.RedisMessagePublisher;
import io.lettuce.core.RedisClient;
import io.lettuce.core.pubsub.StatefulRedisPubSubConnection;
import io.lettuce.core.pubsub.api.sync.RedisPubSubCommands;
import org.junit.Before;
import org.junit.Test;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class RedisMessagePublisherTest {

    private RedisClient mockRedisClient;
    private StatefulRedisPubSubConnection<String, String> mockConnection;
    private RedisPubSubCommands<String, String> mockCommands;
    private RedisMessagePublisher mockPublisher;
    private String mockTopic = "testTopic";
    private String mockMessage = "testMsg";

    @Before
    public void setUp() {
        mockRedisClient = mock(RedisClient.class);
        mockConnection = mock(StatefulRedisPubSubConnection.class);
        mockCommands = mock(RedisPubSubCommands.class);
        when(mockRedisClient.connectPubSub()).thenReturn(mockConnection);
        when(mockConnection.sync()).thenReturn(mockCommands);
        mockPublisher = new RedisMessagePublisher(mockTopic, "uimTokenClearCache", mockRedisClient);
    }

    @Test
    public void publishSync() {
        mockPublisher.publishSync(mockMessage);
        verify(mockCommands).publish(mockTopic, mockMessage);
        verify(mockConnection).close();
    }

    @Test
    public void publishUimTokenClearCache() {
        mockPublisher.publishUimTokenClearCache(mockMessage);
        verify(mockCommands).publish("uimTokenClearCache", mockMessage);
        verify(mockConnection).close();
    }
}
