package com.morningstar.martapi.exception;

import com.morningstar.dataac.martgateway.core.entitlement.exception.EntitlementException;
import com.morningstar.martapi.controller.LicenseAuditController;
import com.morningstar.martgateway.domains.core.entity.investmentresponse.InvestmentResponse;
import com.morningstar.martgateway.domains.core.entity.response.Status;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.core.codec.DecodingException;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.ServerWebInputException;

import static com.morningstar.martapi.exception.utils.ExceptionHandlerUtils.extractProductId;
import static com.morningstar.martapi.exception.utils.ExceptionHandlerUtils.extractStatusCode;

@Order(Ordered.HIGHEST_PRECEDENCE)
@RestControllerAdvice(assignableTypes = {LicenseAuditController.class})
public class LicenseExceptionHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(LicenseExceptionHandler.class);

    @ExceptionHandler(value = ValidationException.class)
    public ResponseEntity<?> handleValidationException(
            ValidationException e,
            ServerWebExchange ex) {
        String url = ex.getRequest().getURI().toString();
        String productId = extractProductId(ex);
        LOGGER.warn("event_type=\"License Validation\", " +
                "event_description=\"Validation failure\", " +
                "url=\"{}\", product_id=\"{}\", e=\"{}\"", url, productId, e.getMessage());
        return ResponseEntity
                .status(extractStatusCode(e.getStatus()))
                .body(e.getStatus());
    }

    @ExceptionHandler(value = EntitlementException.class)
    public ResponseEntity<?> handleEntitlementException(EntitlementException e, ServerWebExchange ex) {
        String url = ex.getRequest().getURI().toString();
        String productId = extractProductId(ex);
        LOGGER.warn("event_type=\"License Authorization Failure\", " +
                "event_description=\"Entitlement retrieval failed\", " +
                "url=\"{}\", product_id=\"{}\", e=\"{}\"", url, productId, e.getMessage());
        return ResponseEntity
                .status(extractStatusCode(e.getStatus()))
                .body(e.getStatus());
    }

    @ExceptionHandler(value = Exception.class)
    public ResponseEntity<?> handleOtherException(Exception e,
                                                  ServerWebExchange ex) {
        String url = ex.getRequest().getURI().toString();
        LOGGER.warn("event_type=\"License Unexpected Exception\", " +
                "event_description=\"Unexpected issue encountered\", " +
                "url=\"{}\", e=\"{}\"", url, e.getMessage());
        return ResponseEntity.internalServerError()
                .body(Status.INTERNAL_ERROR);
    }

    @ExceptionHandler(value = {DecodingException.class, ServerWebInputException.class})
    public ResponseEntity<InvestmentResponse> handleInvalidRequestInput(
            Exception e,
            ServerWebExchange ex) {
        String url = ex.getRequest().getURI().toString();
        String productId = extractProductId(ex);
        LOGGER.warn("event_type=\"Invalid Request Input\", " +
                "event_description=\"Request Input not valid JSON\", " +
                "url=\"{}\", product_id=\"{}\", e=\"{}\"", url, productId, e.getMessage());
        return ResponseEntity.badRequest().body(new InvestmentResponse(Status.INVALID_REQUEST_INPUT, null));
    }
}
