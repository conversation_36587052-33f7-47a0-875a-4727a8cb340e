package com.morningstar.martapi.util;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.Test;

public class AsyncGetStatusUserIdUtilTest {

    private final AsyncGetStatusUserIdUtil util = new AsyncGetStatusUserIdUtil();
    private final String token = "Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6Ik1FWXlOakZDTVRVME0wRkdSRGxCUTBVeE56RTFRamt6TWtaR1JUTTJOME01TlVZelJFWTJOdyJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.SP8kFzdzo4LKVhYiKcKPQXzaCN7cV98xe6C8ix4tMt9bHrKq96WKHaS4YjCZUwfgC7q8yoFZdeJ4RhCFYGjozi2n5VKvmjlRh19uB2nHlyuI9ivxAtyIyHJmZYkjA9lalbEuNtQY2q3MG4HoMBWI3oi7SNXjf7JBc0rcHTyfyyZ4pgFb7pvTjf7cASXu3V4Q8miol0aNCaEx4GbQshkKmmor2Wph-jFrDkHzraklQUKehJCcPiJeIYbo2yMDCAdeoLqisPhPcceSSSGdwZen_10TU4aP32Le820Q0W712V1BzGVNgXgbOVVPg5W8bSDJ1vZT3Ae24E4ZVo62S_5Hkw";
    private final String tokenUserId = "F185649C-4B6A-4D46-9BB4-4AAE4D661220";

    @Test
    public void getTokenUserIdIfHeaderUserIdIsNullOrEmpty() {
        String userId = util.determineUserId(token, null);
        assertEquals(tokenUserId, userId);

        userId = util.determineUserId(token, "");
        assertEquals(tokenUserId, userId);
    }

    @Test
    public void getTokenUserIdIfNotServiceAccount() {
        String userId = util.determineUserId(token, "headerUserId");
        assertEquals(tokenUserId, userId);
    }

    @Test
    public void getHeaderUserIdIfServiceAccount() {
        // At the time of writing this test, the INVESTMENTAPI_INTERNAL_SERVICE_ACCOUNT role is not yet available.
        AsyncGetStatusUserIdUtil modifiedUtil = new AsyncGetStatusUserIdUtil("MD_MEMBER_1_1");
        String userId = modifiedUtil.determineUserId(token, "headerUserId");
        assertEquals("headerUserId", userId);
    }

}