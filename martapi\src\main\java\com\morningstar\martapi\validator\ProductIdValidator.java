package com.morningstar.martapi.validator;

import com.morningstar.martapi.exception.InvestmentApiValidationException;
import com.morningstar.martapi.validator.entity.HeadersAndParams;
import com.morningstar.martgateway.domains.core.entity.response.Status;
import com.morningstar.martgateway.infrastructures.config.ProductIdsRegistry;
import org.springframework.util.StringUtils;

public class ProductIdValidator implements Validator<HeadersAndParams> {

    private final ProductIdsRegistry productIdsRegistry;

    public ProductIdValidator(ProductIdsRegistry productIdsRegistry) {
        this.productIdsRegistry = productIdsRegistry;
    }

    @Override
    public void validate(HeadersAndParams headersAndParams) throws RuntimeException {
        String productId = headersAndParams.getProductId();
        // product id should present in the header
        if (!StringUtils.hasText(productId)) {
            throw new InvestmentApiValidationException(Status.INVALID_PRODUCT_ID);
        }

        // product id registration should not be empty
        if (productIdsRegistry == null || productIdsRegistry.isEmpty()) {
            Status status = new Status(Status.INVALID_PRODUCT_ID.getCode(), "Product id registration is empty, no product id is supported");
            throw new InvestmentApiValidationException(status);
        }

        // product id should be valid
        if (!productIdsRegistry.hasProductId(productId)) {
            throw new InvestmentApiValidationException(Status.INVALID_PRODUCT_ID);
        }
    }
}
