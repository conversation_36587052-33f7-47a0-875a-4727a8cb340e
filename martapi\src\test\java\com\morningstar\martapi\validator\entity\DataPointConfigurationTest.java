package com.morningstar.martapi.validator.entity;

import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import com.morningstar.martcommon.entity.payload.gridview.GridviewDataPoint;

public class DataPointConfigurationTest {

    @Test
    public void equals() {
        DataPointConfiguration dataPointConfiguration1 = DataPointConfiguration.builder()
                .dataPointId("HP010")
                .startDate("2020-01-01")
                .endDate("2021-01-01")
                .extendedPerformance("1")
                .build();


        DataPointConfiguration dataPointConfiguration2 = DataPointConfiguration.builder()
                .dataPointId("HP010")
                .startDate("2020-01-01")
                .endDate("2021-01-01")
                .extendedPerformance("2")
                .build();
        Assertions.assertNotEquals(dataPointConfiguration1, dataPointConfiguration2);

        DataPointConfiguration dataPointConfiguration3 = dataPointConfiguration1;
        Assertions.assertEquals(dataPointConfiguration1, dataPointConfiguration3);
        Assertions.assertNotEquals(dataPointConfiguration1, null);
        Assertions.assertNotEquals(dataPointConfiguration1, GridviewDataPoint.builder().build());
    }

}
