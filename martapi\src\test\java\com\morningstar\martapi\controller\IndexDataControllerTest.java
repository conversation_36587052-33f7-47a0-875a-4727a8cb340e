package com.morningstar.martapi.controller;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.morningstar.martapi.exception.IndexAPIException;
import com.morningstar.martgateway.domains.index.entity.IndexDataResult;
import com.morningstar.martgateway.domains.index.service.IndexDataService;
import com.morningstar.martapi.validator.RequestValidationHandler;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.Collections;
import java.util.UUID;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;



public class IndexDataControllerTest {

    @Mock
    private IndexDataService indexDataService;

    private IndexDataController indexDataController;

    String moduleId = "BTWS000001";
    String token = createToken();
    String requestId = "reqId";
    String productId = "productId";

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        this.indexDataController = new IndexDataController(indexDataService,new RequestValidationHandler<>(Collections.emptyList(), Collections.emptyList()));
    }



    @Test
    public void testGetReferenceIndexDataSuccess() {

        IndexDataResult indexDataResponse = new IndexDataResult();

        when(indexDataService.getData(any(String.class))).thenReturn(Mono.just(indexDataResponse));

        Mono<IndexDataResult> result = indexDataController.getReferenceIndexData(
                moduleId, productId, requestId, token);

        StepVerifier.create(result)
                .expectNext(indexDataResponse)
                .verifyComplete();
    }

    @Test
    public void testGetReferenceIndexDataFailure() {
        when(indexDataService.getData(any(String.class))).thenReturn(Mono.error(new RuntimeException("Test Exception")));
        Mono<IndexDataResult> result = indexDataController.getReferenceIndexData(
                moduleId, productId, requestId, token);
        StepVerifier.create(result)
                .expectError(IndexAPIException.class)
                .verify();
    }


    private String createToken() {
        return JWT.create()
                .withExpiresAt(ZonedDateTime.now(ZoneOffset.UTC).plusDays(1).toInstant())
                .withClaim("https://morningstar.com/mstar_id", UUID.randomUUID().toString())
                .sign(Algorithm.none());
    }
}