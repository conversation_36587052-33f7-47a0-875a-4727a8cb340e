package com.morningstar.martapi.entity;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TranscriptApiRequest {

    @JsonProperty("event_id")
    private String eventId;

    @JsonProperty("job_id")
    private String jobId;

    @JsonProperty("entity_id")
    private String entityId;

    @JsonProperty("event_datetime")
    private String eventDatetime;
}
