package com.morningstar.martapi.validator.portfolioholdings;

import com.morningstar.martapi.exception.HoldingValidationException;
import com.morningstar.dataac.martgateway.data.ph.entity.HoldingDataRequest;
import com.morningstar.martgateway.interfaces.model.investmentapi.Investment;
import org.junit.Test;

import java.util.Set;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertThrows;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;

public class InvestmentCountValidatorTest {

    @Test
    public void testValidate_NullInvestments() {
        // Setup
        HoldingDataRequest request = new HoldingDataRequest();
        request.setInvestments(null);

        InvestmentCountValidator validator = new InvestmentCountValidator();

        // Execute and Assert
        assertThrows(NullPointerException.class, () -> validator.validate(request));
    }

    @Test
    public void testValidate_EmptyInvestments() {
        // Setup
        HoldingDataRequest request = new HoldingDataRequest();
        request.setInvestments(Set.of()); // Empty investments set

        InvestmentCountValidator validator = new InvestmentCountValidator();

        // Execute and Assert
        HoldingValidationException exception = assertThrows(
                HoldingValidationException.class,
                () -> validator.validate(request)
        );

        assertEquals("Exactly one investment identifier is required", exception.getMessage());
        assertEquals("400", exception.getStatus().getCode().substring(0, 3));
    }

    @Test
    public void testValidate_MultipleInvestments() {
        // Setup
        HoldingDataRequest request = new HoldingDataRequest();
        Set<Investment> investments = Set.of(new Investment("id1"), new Investment("id2"));
        request.setInvestments(investments); // More than one investment

        InvestmentCountValidator validator = new InvestmentCountValidator();

        // Execute and Assert
        HoldingValidationException exception = assertThrows(
                HoldingValidationException.class,
                () -> validator.validate(request)
        );

        assertEquals("Exactly one investment identifier is required", exception.getMessage());
        assertEquals("400", exception.getStatus().getCode().substring(0, 3));
    }

    @Test
    public void testValidate_SingleInvestment() {
        // Setup
        HoldingDataRequest request = new HoldingDataRequest();
        Set<Investment> investments = Set.of(new Investment("id1"));
        request.setInvestments(investments); // Exactly one investment

        InvestmentCountValidator validator = new InvestmentCountValidator();

        // Execute and Assert
        assertDoesNotThrow(() -> validator.validate(request));
    }

}