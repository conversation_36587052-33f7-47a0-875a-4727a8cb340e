package com.morningstar.martapi.validator.tsrequest;

import com.morningstar.martapi.exception.ValidationException;
import com.morningstar.martcommon.entity.result.request.MartRequest;
import com.morningstar.martgateway.domains.core.entity.response.Status;
import java.util.List;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;

public class TsMartRequestDataPointValidatorTest {
	private TsMartRequestDataPointValidator validator;

	@Before
	public void setup() {
		this.validator = new TsMartRequestDataPointValidator();
	}

	@Test
	public void validate() {
		MartRequest request = MartRequest.builder()
				.useCase("feed")
				.ids(List.of("OP01010", "OP01012"))
				.startDate("2021-01-01")
				.endDate("2022-01-01")
				.build();
		ValidationException exception = Assertions.assertThrows(ValidationException.class,
				() -> validator.validate(request));
		Assertions.assertEquals(Status.MISSING_ATTRIBUTE.getCode(),exception.getStatus().getCode());
	}

	@Test
	public void validateRequestBodyDuplicateDataPoint() {

		MartRequest request = MartRequest.builder()
				.useCase("feed")
				.ids(List.of("OP01010", "OP01012"))
				.dps(List.of("HP010", "AU002", "HP010"))
				.startDate("2021-01-01")
				.endDate("2022-01-01")
				.build();
		ValidationException exception = Assertions.assertThrows(ValidationException.class,
				() -> validator.validate(request));
		Assertions.assertEquals(Status.DUPLICATE_DATAPOINT.getCode(),exception.getStatus().getCode());
	}
}
