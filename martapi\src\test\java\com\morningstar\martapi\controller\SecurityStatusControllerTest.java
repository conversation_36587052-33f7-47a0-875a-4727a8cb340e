package com.morningstar.martapi.controller;

import com.morningstar.martgateway.util.IdMapUtil;
import com.morningstar.martcommon.entity.result.request.NonEmptyIdMapper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.List;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
@RunWith(MockitoJUnitRunner.class)
public class SecurityStatusControllerTest {
    private IdMapUtil idMapUtil;
    private SecurityStatusController controller;

    @Before
    public void setUp(){
        idMapUtil = mock(IdMapUtil.class);
        controller = new SecurityStatusController(idMapUtil);
    }

    @Test
    public void checkSecurityStatusTest(){
        List<String> idList = Arrays.asList("0P00009T69");
        when(idMapUtil.getIdMappers(idList)).thenReturn(Arrays.asList(new NonEmptyIdMapper("FOUSA00DOU", "{\"CompanyId\":\"0C00001YSB\",\"FundId\":\"FSUSA001JH\",\"PerformanceId\":\"0P00002QW5\",\"PolicyId\":null,\"SecId\":\"FOUSA00DOU\",\"ShareClassId\":\"FOUSA00DOU\",\"StrategyId\":\"STUSA05FF1\",\"MasterPortfolioId\":\"1990\",\"SubAccountId\":null,\"AggregateId\":\"AG0000001A\",\"MorningstarCategoryId\":\"$FOCA$MA$$\",\"GlobalCategoryId\":\"$GC$MODALL\"}")));
        controller.checkSecurityStatus("0P00009T69");
    }
}
