package com.morningstar.martapi.filter;

import org.junit.jupiter.api.Test;
import org.springframework.mock.http.server.reactive.MockServerHttpRequest;
import org.springframework.mock.web.server.MockServerWebExchange;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilterChain;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;


public class GzipDecompressionFilterTest {
    @Test
    public void testFilterAppliesDecompression() {
        WebFilterChain chain = mock(WebFilterChain.class);
        when(chain.filter(any())).thenReturn(Mono.empty());

        MockServerWebExchange exchange = MockServerWebExchange.from(MockServerHttpRequest.post("/path")
                .header("Content-Encoding", "gzip")
                .body("compressed data"));

        GzipDecompressionFilter filter = new GzipDecompressionFilter();

        StepVerifier.create(filter.filter(exchange, chain))
                .expectSubscription()
                .expectComplete()
                .verify();

        verify(chain, times(1)).filter(any(ServerWebExchange.class));
    }
}
