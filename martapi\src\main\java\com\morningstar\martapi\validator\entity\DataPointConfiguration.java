package com.morningstar.martapi.validator.entity;

import com.morningstar.martcommon.entity.payload.gridview.GridviewDataPoint;
import lombok.Builder;
import lombok.Data;

import java.util.Objects;

@Data
@Builder
public class DataPointConfiguration {

    private final String dataPointId;
    private final String alias;
    private final String frequency;
    private final String startDate;
    private final String endDate;
    private final String preCurrency;
    private final String currency;
    private final String requireContinueData;
    private final String postTax;
    private final String dateFormat;
    private final String decimalFormat;
    private final String extendedPerformance;

    public static DataPointConfiguration extract(GridviewDataPoint dataPoint) {
        return DataPointConfiguration.builder()
                .dataPointId(dataPoint.getDataPointId())
                .alias(dataPoint.getAlias())
                .frequency(dataPoint.getFrequency())
                .startDate(dataPoint.getStartDate())
                .endDate(dataPoint.getEndDate())
                .preCurrency(dataPoint.getPreCurrency())
                .currency(dataPoint.getCurrency())
                .requireContinueData(dataPoint.getRequireContinueData())
                .postTax(dataPoint.getPostTax())
                .dateFormat(dataPoint.getDateFormat())
                .decimalFormat(dataPoint.getDecimalFormat())
                .extendedPerformance(dataPoint.getExtendedPerformance())
                .build();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        DataPointConfiguration that = (DataPointConfiguration) o;
        return Objects.equals(dataPointId, that.dataPointId) && Objects.equals(alias, that.alias) && Objects.equals(frequency, that.frequency) && Objects.equals(startDate, that.startDate) && Objects.equals(endDate, that.endDate) && Objects.equals(preCurrency, that.preCurrency) && Objects.equals(currency, that.currency) && Objects.equals(requireContinueData, that.requireContinueData) && Objects.equals(postTax, that.postTax) && Objects.equals(dateFormat, that.dateFormat) && Objects.equals(decimalFormat, that.decimalFormat) && Objects.equals(extendedPerformance, that.extendedPerformance);
    }

    @Override
    public int hashCode() {
        return Objects.hash(dataPointId, alias, frequency, startDate, endDate, preCurrency, currency, requireContinueData, postTax, dateFormat, decimalFormat, extendedPerformance);
    }
}
