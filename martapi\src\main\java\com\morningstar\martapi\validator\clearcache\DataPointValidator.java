package com.morningstar.martapi.validator.clearcache;

import com.morningstar.martapi.exception.ValidationException;
import com.morningstar.martapi.validator.Validator;
import com.morningstar.martcommon.entity.ClearCacheRequest;
import com.morningstar.martgateway.domains.core.entity.response.Status;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

public class DataPointValidator implements Validator<ClearCacheRequest> {

    @Override
    public void validate(ClearCacheRequest request) throws ValidationException {
        List<String> dataPoints = request.getDataPoints();
        List<String> invalidIds = dataPoints == null ? dataPoints : dataPoints.stream().filter(StringUtils::isEmpty).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dataPoints) || CollectionUtils.isNotEmpty(invalidIds)) {
            Status status = Status.MISSING_ATTRIBUTE
                    .withMessage("Request input missing mandatory attribute - dataPoints");
            throw new ValidationException(status);
        }
    }
}