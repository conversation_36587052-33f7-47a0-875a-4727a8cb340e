package com.morningstar.martapi.validator.portfolioholdings;

import static org.junit.Assert.*;

import com.morningstar.martapi.exception.HoldingValidationException;
import com.morningstar.dataac.martgateway.data.ph.entity.HoldingDataRequest;
import com.morningstar.martgateway.interfaces.model.investmentapi.Investment;

import java.util.HashSet;
import java.util.Set;
import org.junit.Test;

public class InvestmentValidatorTest {

    @Test
    public void validate() {
        HoldingDataRequest request = new HoldingDataRequest();
        InvestmentValidator validator = new InvestmentValidator();
        HoldingValidationException exception = assertThrows(
                HoldingValidationException.class, () -> validator.validate(request));
        assertEquals("Mandatory field `investments` is missing or empty", exception.getMessage());
        assertEquals("400",exception.getStatus().getCode());

        Investment investment = new Investment();
        request.setInvestments(Set.of(investment));
        exception = assertThrows(
                HoldingValidationException.class, () -> validator.validate(request));
        assertEquals("Mandatory field `investments` is missing or empty", exception.getMessage());
        assertEquals("400",exception.getStatus().getCode());

        investment.setId("someId");
        request.setInvestments(Set.of(investment));
        validator.validate(request);
    }

    @Test
    public void validateDuplicateInvestments() {
        HoldingDataRequest request = new HoldingDataRequest();
        InvestmentValidator validator = new InvestmentValidator();

        Investment investment1 = new Investment();
        investment1.setId("someId");
        Investment investment2 = new Investment();
        investment2.setId("someId");

        // Create a HashSet and add the investments
        Set<Investment> investments = new HashSet<>();
        investments.add(investment1);
        investments.add(investment2);
        request.setInvestments(investments);

        assertEquals(1, request.getInvestments().size());
        validator.validate(request);
    }

}