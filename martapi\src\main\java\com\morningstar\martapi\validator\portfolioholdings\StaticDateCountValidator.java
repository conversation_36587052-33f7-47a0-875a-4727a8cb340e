package com.morningstar.martapi.validator.portfolioholdings;

import com.morningstar.dataac.martgateway.data.ph.entity.HoldingDataRequest;
import com.morningstar.dataac.martgateway.data.ph.entity.PortfolioDate;
import com.morningstar.dataac.martgateway.data.ph.entity.enums.PortfolioDateType;
import com.morningstar.martapi.exception.HoldingValidationException;
import com.morningstar.martapi.validator.Validator;
import com.morningstar.martgateway.domains.core.entity.response.Status;

public class StaticDateCountValidator implements Validator<HoldingDataRequest> {

    private final int maxStaticDates;

    public StaticDateCountValidator(int maxStaticDates) {
        this.maxStaticDates = maxStaticDates;
    }

    @Override
    public void validate(HoldingDataRequest request) throws RuntimeException {
        PortfolioDate portfolioDate = request.getPortfolioSetting().getPortfolioDate();
        if (portfolioDate.getType() == PortfolioDateType.STATIC_DATES && portfolioDate.getStaticDates().size() > maxStaticDates) {
            throw new HoldingValidationException(
                    Status.BAD_REQUEST.withMessage("Cannot have more than " + maxStaticDates + " `dates` for type `staticDates`")
            );
        }
    }
}