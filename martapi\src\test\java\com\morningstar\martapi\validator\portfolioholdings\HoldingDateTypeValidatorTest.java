package com.morningstar.martapi.validator.portfolioholdings;

import com.morningstar.dataac.martgateway.data.ph.entity.PortfolioDate;
import com.morningstar.dataac.martgateway.data.ph.entity.PortfolioSetting;
import com.morningstar.dataac.martgateway.data.ph.entity.enums.PortfolioDateType;
import com.morningstar.martapi.exception.HoldingValidationException;
import com.morningstar.dataac.martgateway.data.ph.entity.HoldingDataRequest;
import org.junit.Test;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertThrows;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;

public class HoldingDateTypeValidatorTest {

    @Test
    public void testValidate_TimeSeriesType() {
        // Setup
        HoldingDataRequest request = new HoldingDataRequest();
        PortfolioSetting portfolioSetting = new PortfolioSetting();
        PortfolioDate portfolioDate = new PortfolioDate();
        portfolioDate.setType(PortfolioDateType.TIME_SERIES);
        portfolioSetting.setPortfolioDate(portfolioDate);
        request.setPortfolioSetting(portfolioSetting);

        HoldingDateTypeValidator validator = new HoldingDateTypeValidator();

        // Execute and Assert
        HoldingValidationException exception = assertThrows(
                HoldingValidationException.class,
                () -> validator.validate(request)
        );

        assertEquals("Type `timeSeries` is not allowed for this endpoint", exception.getMessage());
        assertEquals("400", exception.getStatus().getCode());
    }

    @Test
    public void testValidate_NonTimeSeriesType() {
        // Setup
        HoldingDataRequest request = new HoldingDataRequest();
        PortfolioSetting portfolioSetting = new PortfolioSetting();
        PortfolioDate portfolioDate = new PortfolioDate();
        portfolioDate.setType(PortfolioDateType.STATIC_DATES);
        portfolioSetting.setPortfolioDate(portfolioDate);
        request.setPortfolioSetting(portfolioSetting);

        HoldingDateTypeValidator validator = new HoldingDateTypeValidator();

        // Execute and Assert
        assertDoesNotThrow(() -> validator.validate(request));
    }

}