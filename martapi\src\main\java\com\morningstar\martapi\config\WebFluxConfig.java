package com.morningstar.martapi.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.accept.RequestedContentTypeResolverBuilder;
import org.springframework.web.reactive.config.WebFluxConfigurer;

@Configuration
public class Web<PERSON>luxConfig implements WebFluxConfigurer {

    @Override
    public void configureContentTypeResolver(RequestedContentTypeResolverBuilder builder) {
        builder.parameterResolver().
                parameterName("format")
                .mediaType("xml", MediaType.APPLICATION_XML)
                .mediaType("json", MediaType.APPLICATION_JSON)
                .mediaType("0", MediaType.parseMediaType("application/x-protobuf"))
                .mediaType("1", MediaType.APPLICATION_JSON)
                .mediaType("2", MediaType.APPLICATION_XML);
    }
}