package com.morningstar.martapi.validator.portfolioholdings;

import com.morningstar.dataac.martgateway.data.ph.entity.HoldingDataRequest;
import com.morningstar.martapi.exception.HoldingValidationException;
import com.morningstar.martapi.validator.Validator;
import com.morningstar.martgateway.domains.core.entity.response.Status;

public class HoldingsDataPointsCountValidator implements Validator<HoldingDataRequest> {
    private static final int MAX_DATA_POINTS = 1000;

    @Override
    public void validate(HoldingDataRequest request) throws RuntimeException {
        if (request.getPortfolioSetting().getHoldingsDataPoints().size() > MAX_DATA_POINTS) {
            Status status = Status.BAD_REQUEST
                    .withMessage("A maximum of 1000 data points are allowed");
            throw new HoldingValidationException(status);
        }
    }
}
