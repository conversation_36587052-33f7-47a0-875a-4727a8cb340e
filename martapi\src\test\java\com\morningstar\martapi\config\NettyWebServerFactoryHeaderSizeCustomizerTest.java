package com.morningstar.martapi.config;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.boot.web.embedded.netty.NettyReactiveWebServerFactory;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;

@RunWith(MockitoJUnitRunner.class)
public class NettyWebServerFactoryHeaderSizeCustomizerTest {
    @Mock
    private NettyReactiveWebServerFactory container;
    private NettyWebServerFactoryHeaderSizeCustomizer nettyWebServerFactoryHeaderSizeCustomizer;


    @Test
    public void shouldCustomizeTest() {

        doNothing().when(container).addServerCustomizers(any());
        nettyWebServerFactoryHeaderSizeCustomizer = new NettyWebServerFactoryHeaderSizeCustomizer();
        nettyWebServerFactoryHeaderSizeCustomizer.customize(container);
    }

}
