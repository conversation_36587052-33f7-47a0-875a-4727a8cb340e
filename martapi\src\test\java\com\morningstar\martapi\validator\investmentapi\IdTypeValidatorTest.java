package com.morningstar.martapi.validator.investmentapi;

import com.morningstar.martapi.exception.InvestmentApiValidationException;
import com.morningstar.martgateway.domains.core.entity.response.Status;
import com.morningstar.martcommon.entity.payload.gridview.GridviewDataPoint;
import com.morningstar.martgateway.interfaces.model.investmentapi.Investment;
import com.morningstar.martgateway.interfaces.model.investmentapi.InvestmentApiRequest;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;

import java.util.List;

public class IdTypeValidatorTest {

    private IdTypeValidator validator;

    @Before
    public void setup() {
        this.validator = new IdTypeValidator();
    }

    @Test
    public void validate() {
        InvestmentApiRequest request = InvestmentApiRequest.builder()
                .useCase("view")
                .idType("CompanyId")
                .investments(List.of(Investment.builder().id("10330020").build(),Investment.builder().id("10330021").build()))
                .dataPoints(List.of(GridviewDataPoint.builder().dataPointId("EQ2Y7").build(), GridviewDataPoint.builder().dataPointId("EQ2Y8").build()))
                .build();
        Assertions.assertDoesNotThrow(() -> validator.validate(request));
    }

    @Test
    public void validateUnknown() {
        InvestmentApiRequest request = InvestmentApiRequest.builder()
                .useCase("view")
                .idType("unknown")
                .investments(List.of(Investment.builder().id("10330020").build(),Investment.builder().id("10330021").build()))
                .dataPoints(List.of(GridviewDataPoint.builder().dataPointId("EQ2Y7").build(), GridviewDataPoint.builder().dataPointId("EQ2Y8").build()))
                .build();
        InvestmentApiValidationException exception = Assertions.assertThrows(InvestmentApiValidationException.class,
                () -> validator.validate(request));
        Assertions.assertEquals(Status.INVALID_ID_TYPE.getCode(),exception.getStatus().getCode());
    }
}
