package com.morningstar.martapi.validator.portfolioholdings;

import com.morningstar.dataac.martgateway.data.ph.entity.HoldingDataRequest;
import com.morningstar.martapi.exception.HoldingValidationException;
import com.morningstar.martapi.validator.Validator;
import com.morningstar.martgateway.domains.core.entity.response.Status;
import com.morningstar.martgateway.interfaces.model.investmentapi.Investment;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

public class InvestmentValidator implements Validator<HoldingDataRequest> {

    @Override
    public void validate(HoldingDataRequest request) throws RuntimeException {
        Set<Investment> investments = request.getInvestments();
        if (CollectionUtils.isEmpty(investments)) {
            throw new HoldingValidationException(Status.BAD_REQUEST.withMessage("Mandatory field `investments` is missing or empty"));
        }
        Set<Investment> nonEmptyInvestments = investments
                .stream()
                .filter(i -> !StringUtils.isEmpty(i.getId()))
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(nonEmptyInvestments)) {
            throw new HoldingValidationException(Status.BAD_REQUEST.withMessage("Mandatory field `investments` is missing or empty"));
        }
    }
}
