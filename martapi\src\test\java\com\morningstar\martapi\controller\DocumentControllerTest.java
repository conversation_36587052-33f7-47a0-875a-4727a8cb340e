package com.morningstar.martapi.controller;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.morningstar.martapi.service.DocumentService;
import com.morningstar.martapi.validator.RequestValidationHandler;
import com.morningstar.martapi.validator.entity.HeadersAndParams;
import com.morningstar.martgateway.interfaces.model.investmentapi.InvestmentApiRequest;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.MockitoAnnotations;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.http.server.reactive.MockServerHttpRequest;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.Collections;
import java.util.UUID;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class DocumentControllerTest {

    private DocumentController documentController;
    private DocumentService documentService;

    String performanceId = "0P0000077G";
    //String eventDate = "2024-10-31T00:00:00Z";
    String readCache = "true";
    String headerUserId = "userId";
    String token = createToken();
    String requestId = "reqId";
    String productId = "productId";
    String checkEntitlement = "true";
    String documentType = "EQ5NQ";
    String documentId = "1410374";
    boolean skipProxy = false;

    @Before
    public void setup() {
        documentService = mock(DocumentService.class);
        documentController = new DocumentController(new RequestValidationHandler<>(Collections.emptyList(), Collections.emptyList()), documentService);
    }

    @Test
    public void testGetDataForSuccess() {
        MockitoAnnotations.openMocks(this);

        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream("{\"key\":\"value\"}".getBytes(StandardCharsets.UTF_8));
        Mono<ByteArrayInputStream> mockMonoInputStream = Mono.just(byteArrayInputStream);

        when(documentService.getDocument(any(InvestmentApiRequest.class), any(HeadersAndParams.class), any(Long.class), any(String.class)))
                .thenReturn(mockMonoInputStream);

        Mono<ResponseEntity<InputStreamResource>> responseEntityMono = documentController.getData(
                performanceId, documentType, documentId, readCache, headerUserId, token, requestId, productId, checkEntitlement, skipProxy,MockServerHttpRequest.get("/").build()
        );

        ResponseEntity<InputStreamResource> responseEntity = responseEntityMono.block();
        assertNotNull(responseEntity);
        Assert.assertEquals(HttpStatus.OK, responseEntity.getStatusCode());
        Assert.assertEquals("application/json", responseEntity.getHeaders().getFirst(HttpHeaders.CONTENT_TYPE));
    }

    @Test(expected = NullPointerException.class)
    public void testGetDataForExceptionHandling() {
        MockitoAnnotations.openMocks(this);

        when(documentService.getDocument(any(InvestmentApiRequest.class), any(HeadersAndParams.class), any(Long.class), any(String.class)))
                .thenReturn(null);

        Mono<ResponseEntity<InputStreamResource>> responseEntityMono = documentController.getData(
                performanceId, documentType, documentId, readCache, headerUserId, token, requestId, productId, checkEntitlement, skipProxy,MockServerHttpRequest.get("/").build()
        );

        Assert.assertNull(responseEntityMono);
    }

    @Test
    public void testGetDataForDocumentTypeExceptionHandling() {
        MockitoAnnotations.openMocks(this);

        Mono<ResponseEntity<InputStreamResource>> responseEntityMono = documentController.getData(
                performanceId, "QE12234", documentId, readCache, headerUserId, token, requestId, productId, checkEntitlement, skipProxy,MockServerHttpRequest.get("/").build()
        );

        StepVerifier.create(responseEntityMono)
                .expectErrorMatches(throwable ->
                        throwable instanceof IllegalArgumentException &&
                                throwable.getMessage().equals("Invalid documentType: Correct the documentType, example: EQ5NQ"))
                .verify();
    }

    @Test
    public void testGetDataForDocumentIdExceptionHandling() {
        MockitoAnnotations.openMocks(this);

        Mono<ResponseEntity<InputStreamResource>> responseEntityMono = documentController.getData(
                performanceId, documentType, "", readCache, headerUserId, token, requestId, productId, checkEntitlement, skipProxy, MockServerHttpRequest.get("/").build()
        );

        StepVerifier.create(responseEntityMono)
                .expectErrorMatches(throwable ->
                        throwable instanceof IllegalArgumentException &&
                                throwable.getMessage().equals("Invalid documentId: Correct the documentId, example: 164531"))
                .verify();
    }

    @Test
    public void testGetDataForProductIdExceptionHandling() {
        MockitoAnnotations.openMocks(this);

        Mono<ResponseEntity<InputStreamResource>> responseEntityMono = documentController.getData(
                "0P123456789", documentType, documentId, readCache, headerUserId, token, requestId, productId, checkEntitlement, skipProxy,MockServerHttpRequest.get("/").build()
        );

        StepVerifier.create(responseEntityMono)
                .expectErrorMatches(throwable ->
                        throwable instanceof IllegalArgumentException &&
                                throwable.getMessage().equals("Invalid performanceId: Correct the performanceID, example: 0P00000001"))
                .verify();
    }

    private String createToken() {
        return JWT.create()
                .withExpiresAt(ZonedDateTime.now(ZoneOffset.UTC).plusDays(1).toInstant())
                .withClaim("https://morningstar.com/mstar_id", UUID.randomUUID().toString())
                .sign(Algorithm.none());
    }


}