package com.morningstar.martapi.repo;

import com.morningstar.martgateway.interfaces.model.AsyncDbDetails;
import javax.inject.Inject;
import javax.inject.Named;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.model.PutItemEnhancedRequest;


@Named
public class DynamoDao {

    private final DynamoDbTable<AsyncDbDetails> dynamoDbTable;
    private static final Logger log = LoggerFactory.getLogger(DynamoDao.class);

    @Inject
    public DynamoDao(DynamoDbTable<AsyncDbDetails> dynamoDbTable) {
        this.dynamoDbTable = dynamoDbTable;

    }

    public void upsertAsyncRecord(AsyncDbDetails asyncDBDetails) {
        PutItemEnhancedRequest<AsyncDbDetails> request = PutItemEnhancedRequest.builder(AsyncDbDetails.class)
                .item(asyncDBDetails)
                .build();
        dynamoDbTable.putItem(request);
    }

    public AsyncDbDetails getAsyncRecord(String jobId) {
        Key key = Key.builder()
                .partitionValue(jobId)
                .build();
        return dynamoDbTable.getItem(key);
    }
}
