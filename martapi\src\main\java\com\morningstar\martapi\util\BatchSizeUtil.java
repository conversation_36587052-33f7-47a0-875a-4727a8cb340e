package com.morningstar.martapi.util;

import com.morningstar.dataac.martgateway.data.ph.entity.HoldingBufferSize;
import com.morningstar.dataac.martgateway.data.ph.entity.HoldingDataAsyncInput;
import com.morningstar.martgateway.interfaces.model.AsyncInput;
import lombok.Getter;

@Getter
public class BatchSizeUtil {

   private Integer blBatchSize;
   private Integer ltBatchSize;

   public BatchSizeUtil(AsyncInput asyncInput) {
       if (asyncInput instanceof HoldingDataAsyncInput holdingInput) {
           blBatchSize = holdingInput.getHoldingDataRequest().getBlBatchSize() == null ?
                   HoldingBufferSize.getDefaultBlHoldingBufferSize() : holdingInput.getHoldingDataRequest().getBlBatchSize();
           ltBatchSize = holdingInput.getHoldingDataRequest().getLtBatchSize() == null ?
                   HoldingBufferSize.getDefaultLtHoldingBufferSize() : holdingInput.getHoldingDataRequest().getLtBatchSize();
       }
   }
}
