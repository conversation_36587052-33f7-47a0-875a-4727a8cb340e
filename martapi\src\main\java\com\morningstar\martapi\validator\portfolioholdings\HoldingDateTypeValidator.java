package com.morningstar.martapi.validator.portfolioholdings;

import com.morningstar.dataac.martgateway.data.ph.entity.HoldingDataRequest;
import com.morningstar.dataac.martgateway.data.ph.entity.PortfolioDate;
import com.morningstar.dataac.martgateway.data.ph.entity.enums.PortfolioDateType;
import com.morningstar.martapi.exception.HoldingValidationException;
import com.morningstar.martapi.validator.Validator;
import com.morningstar.martgateway.domains.core.entity.response.Status;

public class HoldingDateTypeValidator implements Validator<HoldingDataRequest> {

    @Override
    public void validate(HoldingDataRequest request) throws RuntimeException {
        PortfolioDate portfolioDate = request.getPortfolioSetting().getPortfolioDate();
        PortfolioDateType type = portfolioDate.getType();

        // Prohibit "timeSeries" for the type field under portfolioDate.
        if (type == PortfolioDateType.TIME_SERIES) {
            throw new HoldingValidationException(
                    Status.BAD_REQUEST.withMessage("Type `timeSeries` is not allowed for this endpoint")
            );
        }
    }
}
