FROM azul/zulu-openjdk-alpine:17-latest

COPY --from=hengyunabc/arthas:latest /opt/arthas /opt/arthas
COPY target/martapi.jar /opt/martapi/martapi.jar
COPY ./start.sh /opt/martapi/start.sh
COPY ./libCalculation_CPP.so /usr/lib/libCalculation_CPP.so

EXPOSE 8080

WORKDIR /opt/martapi/

RUN wget https://s3.amazonaws.com/mstar-prod-repos/software/newrelic/java-agent/newrelic-java.zip && \
    unzip newrelic-java.zip && \
    mkdir -p /opt/newrelic/java/ && \
    cp ./newrelic/newrelic.jar /opt/newrelic/java/ && \
    cp ./newrelic/newrelic.yml /opt/newrelic/java/

ENV NEW_RELIC_LICENSE_KEY="87be58ac7590a478f69935757dbfb1cd4c2850af" \
    MAX_TRANSACTION_SAMPLES_STORED=500 \
    MAX_EVENT_SAMPLES_STORED=500 \
    NEW_RELIC_SPAN_EVENTS_ENABLED=true \
    NEW_RELIC_SPAN_EVENTS_MAX_SAMPLES_STORED=500

RUN apk add libstdc++
RUN apk add --upgrade openssl
RUN apk upgrade musl-utils

ENTRYPOINT ["/bin/sh", "start.sh"]