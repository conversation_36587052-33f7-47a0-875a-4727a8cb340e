package com.morningstar.martapi.validator.clearcache;

import com.morningstar.martapi.exception.ValidationException;
import com.morningstar.martcommon.entity.ClearCacheRequest;
import com.morningstar.martgateway.domains.core.entity.response.Status;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;

import java.util.List;

public class DataPointValidatorTest {

    private DataPointValidator validator;

    @Before
    public void setup() {
        this.validator = new DataPointValidator();
    }

    @Test
    public void validateDataPoint() {
        ClearCacheRequest request = ClearCacheRequest.builder().dataPoints(List.of("dp1","dp2")).build();
        validator.validate(request);
    }

    @Test
    public void validateMissingDataPoint() {
        ClearCacheRequest request = ClearCacheRequest.builder()
                .investmentIds(List.of("id1","id2")).build();
        ValidationException exception = Assertions.assertThrows(ValidationException.class,
                () -> validator.validate(request));
        Assertions.assertEquals(Status.MISSING_ATTRIBUTE.getCode(),exception.getStatus().getCode());
    }
}