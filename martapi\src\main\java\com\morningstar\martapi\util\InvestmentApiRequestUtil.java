package com.morningstar.martapi.util;

import com.morningstar.martapi.validator.entity.HeadersAndParams;
import com.morningstar.martgateway.domains.core.entity.investmentresponse.InvestmentResponse;
import com.morningstar.martgateway.infrastructures.log.LogHelper;
import com.morningstar.martgateway.interfaces.MartGateway;
import com.morningstar.martgateway.interfaces.model.investmentapi.InvestmentApiRequest;
import com.morningstar.martgateway.util.JwtUtil;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.UUID;

@Component
@NoArgsConstructor
public class InvestmentApiRequestUtil {

    public Mono<InvestmentResponse> getInvestmentResponse(MartGateway<InvestmentResponse, InvestmentApiRequest> gridViewGateway, InvestmentApiRequest investmentApiRequest, HeadersAndParams headersAndParams, long startTime) {
        LoggerUtil loggerUtil = new LoggerUtil();
        return gridViewGateway.asyncRetrieveSecurities(investmentApiRequest)
                .doOnEach(LogHelper.logOnNext(list -> loggerUtil.logAccess(investmentApiRequest, headersAndParams, startTime)))
                .doOnEach(LogHelper.logOnError(e -> loggerUtil.logError(investmentApiRequest, headersAndParams, startTime, e)));
    }

    public HeadersAndParams getHeaderAndParams(String token, String productId, String requestId) {
        return HeadersAndParams.builder()
                .authorizationToken(token)
                .productId(productId)
                .requestId(requestId)
                .build();
    }

    public InvestmentApiRequest getValidatedInvestmentApiRequest(InvestmentApiRequest investmentApiRequest, String token, String headerUserId, String productId, String requestId, String readCache, String checkEntitlement) {
        String userId = StringUtils.defaultIfEmpty(JwtUtil.getFieldValue(token, "https://morningstar.com/mstar_id"), headerUserId);
        String configId = JwtUtil.getFieldValue(token, "https://morningstar.com/config_id");
        investmentApiRequest.setUserId(userId);
        investmentApiRequest.setConfigId(configId);
        investmentApiRequest.setProductId(productId);
        String reqId = StringUtils.isNotEmpty(requestId) ? requestId : UUID.randomUUID().toString();
        investmentApiRequest.setRequestId(reqId);
        investmentApiRequest.setReadCache(readCache);
        investmentApiRequest.setCheckEntitlement("true".equals(checkEntitlement));
        return investmentApiRequest;
    }
}
