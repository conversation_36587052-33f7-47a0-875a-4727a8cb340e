package com.morningstar.martapi.validator.investmentapi;

import com.morningstar.martapi.exception.InvestmentApiValidationException;
import com.morningstar.martapi.validator.Validator;
import com.morningstar.martgateway.domains.core.entity.response.Status;
import com.morningstar.martgateway.interfaces.model.investmentapi.Investment;
import com.morningstar.martgateway.interfaces.model.investmentapi.InvestmentApiRequest;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class InvestmentValidator implements Validator<InvestmentApiRequest> {

    @Override
    public void validate(InvestmentApiRequest request) throws InvestmentApiValidationException {
        List<Investment> investments = request.getInvestments();
        if (CollectionUtils.isEmpty(investments)) {
            Status status = Status.MISSING_ATTRIBUTE
                    .withMessage("Request input missing mandatory attribute - investments");
            throw new InvestmentApiValidationException(status);
        }
        Set<Investment> distinctInvestments = new HashSet<>();
        for (Investment investment : investments) {
            if (StringUtils.isEmpty(investment.getId())) {
                Status status = Status.MISSING_ATTRIBUTE
                        .withMessage("Request input missing mandatory attribute - investment id");
                throw new InvestmentApiValidationException(status);
            }
            if (!distinctInvestments.add(investment)) {
                Status status = Status.DUPLICATE_INVESTMENT;
                throw new InvestmentApiValidationException(status);
            }
        }
    }
}
