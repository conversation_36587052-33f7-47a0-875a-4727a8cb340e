package com.morningstar.martapi.grpc;

import com.morningstar.martapi.exception.TsCacheProtobufValidationException;
import com.morningstar.martapi.exception.ValidationException;
import com.morningstar.martapi.validator.RequestValidationHandler;
import com.morningstar.martapi.validator.entity.HeadersAndParams;
import com.morningstar.martcommon.entity.result.request.MartRequest;
import com.morningstar.martcommon.entity.result.response.TimeseriesData;
import com.morningstar.martcommon.entity.result.response.TimeseriesPair;
import com.morningstar.martgateway.domains.core.entity.investmentresponse.Investment;
import com.morningstar.martgateway.domains.core.entity.investmentresponse.InvestmentResponse;
import com.morningstar.martgateway.domains.core.entity.response.Status;
import com.morningstar.martgateway.domains.timeseries.entity.TSItem;
import com.morningstar.martgateway.domains.timeseries.entity.TSResponse;
import com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf;
import com.morningstar.martgateway.infrastructures.log.LogAttribute;
import com.morningstar.martgateway.infrastructures.log.LogEntity;
import com.morningstar.martgateway.infrastructures.log.LogEntry;
import com.morningstar.martgateway.interfaces.MartGateway;
import com.morningstar.martgateway.util.JsonUtils;
import com.morningstar.martgateway.util.JwtUtil;
import io.grpc.stub.StreamObserver;
import net.devh.boot.grpc.server.service.GrpcService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;

import org.springframework.beans.factory.annotation.Qualifier;


import java.util.ArrayList;

import java.util.List;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.morningstar.martapi.config.Constant.LOG_REQUEST_PAYLOAD_LIMIT_MILLISECONDS;
import static com.morningstar.martapi.config.Constant.RESPONSE;
import static com.morningstar.martapi.config.Constant.RESPONSE_ERROR;
import static com.morningstar.martapi.config.Constant.SUCCESSFUL_RESPONSE;

import static com.morningstar.martgateway.infrastructures.log.LogAttribute.*;


@GrpcService
public class TimeSeriesGrpcService extends com.morningstar.martapi.grpc.TimeSeriesServiceGrpc.TimeSeriesServiceImplBase {
    private final MartGateway<InvestmentResponse, MartRequest> tsNewRspGateway;
    private final MartGateway<TSResponse, MartRequest> tsOldRspGateway;
    private final RequestValidationHandler<HeadersAndParams, MartRequest> validator;

    private static final String REQUEST_TYPE_VALUE = "timeseries_grpc";

    public TimeSeriesGrpcService(@Qualifier("tsNewRspGateway") MartGateway<InvestmentResponse, MartRequest> tsNewRspGateway,
                                 MartGateway<TSResponse, MartRequest> tsOldRspGateway,
                                 @Qualifier("timeSeriesApiValidator") RequestValidationHandler<HeadersAndParams, MartRequest> validator) {
        this.tsNewRspGateway = tsNewRspGateway;
        this.tsOldRspGateway = tsOldRspGateway;
        this.validator = validator;
    }

    @Override
    public void retrieveTimeSeriesData(com.morningstar.martapi.grpc.TimeSeriesRequest request, StreamObserver<com.morningstar.martapi.grpc.OldTimeSeriesResponse> responseObserver) {
        long startTime = System.currentTimeMillis();
        MartRequest martRequest = convertToMartRequest(request);

        try {
            HeadersAndParams headersAndParams = convertToHeadersAndParams(request);
            validateRequest(martRequest, headersAndParams);

            handleAsyncRetrieveSecurities(martRequest, startTime, responseObserver, this::convertToOldTimeSeriesResponse);
        } catch (Exception e) {
            handleException(e, martRequest, startTime, responseObserver);
        }
    }

    @Override
    public void retrieveTimeSeriesDataAsProtobuf(com.morningstar.martapi.grpc.TimeSeriesRequest request, StreamObserver<TsCacheDataForProtoBuf.TimeSeriesDatas> responseObserver)  {
        long startTime = System.currentTimeMillis();
        MartRequest martRequest = convertToMartRequest(request);
        try {

            HeadersAndParams headersAndParams = convertToHeadersAndParams(request);
            validateRequest(martRequest, headersAndParams);

            handleAsyncRetrieveSecurities(martRequest, startTime, responseObserver, TSResponse::toProtobuf);
        } catch (Exception e) {
            handleException(e, martRequest, startTime, responseObserver);
        }
    }


    @Override
    public void getInvestmentTSData(com.morningstar.martapi.grpc.TimeSeriesRequest request, StreamObserver<com.morningstar.martapi.grpc.TimeSeriesResponse> responseObserver) {
        long startTime = System.currentTimeMillis();
        MartRequest martRequest = convertToMartRequest(request);

        try {
            HeadersAndParams headersAndParams = convertToHeadersAndParams(request);
            validateRequest(martRequest, headersAndParams);

            tsNewRspGateway.asyncRetrieveSecurities(martRequest)
                    .map(this::convertToTimeSeriesResponse)
                    .subscribe(
                            result -> {
                                responseObserver.onNext(result);
                                responseObserver.onCompleted();
                                logSuccess(martRequest, startTime);
                            },
                            error -> {
                                responseObserver.onError(error);
                                logError(martRequest, startTime, error);
                            }
                    );
        } catch (Exception e) {
            handleException(e, martRequest, startTime, responseObserver);
        }
    }

    private MartRequest convertToMartRequest(com.morningstar.martapi.grpc.TimeSeriesRequest grpcRequest) {
        String userId = getUserIdFromToken(grpcRequest.getUserId(), grpcRequest.getAuthorization());
        String configId = getConfigIdFromToken(grpcRequest.getAuthorization());


        List<String> investmentIds = grpcRequest.getInvestmentIdsList();
        List<String> dataPoints = grpcRequest.getDataPointsList();

        return MartRequest.builder()
                .currency(grpcRequest.getCurrency())
                .dps(dataPoints)
                .ids(investmentIds)
                .startDate(grpcRequest.getStartDate())
                .endDate(grpcRequest.getEndDate())
                .preCurrency(grpcRequest.getPreCurrency())
                .readCache(grpcRequest.getReadCache())
                .productId(grpcRequest.getProductId())
                .entitlementProductId(grpcRequest.getEntitlementProductId())
                .requestId(grpcRequest.getRequestId())
                .userId(userId)
                .dateFormat(grpcRequest.getDateFormat())
                .decimalFormat(grpcRequest.getDecimalFormat())
                .extendedPerformance(grpcRequest.getExtendPerformance())
                .postTax(grpcRequest.getPostTax())
                .useRequireId(grpcRequest.getUseRequireId())
                .checkEntitlement(grpcRequest.getCheckEntitlement())
                .useCase(grpcRequest.getUseCase())
                .useNewCCS(grpcRequest.getUseNewCcs())
                .configId(configId)
                .build();
    }

    private HeadersAndParams convertToHeadersAndParams(com.morningstar.martapi.grpc.TimeSeriesRequest grpcRequest) {
        return HeadersAndParams.builder()
                .authorizationToken(grpcRequest.getAuthorization())
                .productId(grpcRequest.getProductId())
                .requestId(grpcRequest.getRequestId())
                .build();
    }

    private void validateRequest(MartRequest martRequest, HeadersAndParams headersAndParams) {
        try {
            validator.validateHeadersAndParams(headersAndParams);
            validator.validateRequestBody(martRequest);
        } catch (ValidationException e) {
            throw new TsCacheProtobufValidationException(e.getStatus());
        }
    }

    private static String getUserIdFromToken(String headerUserId, String token) {

        String tokenUserId = JwtUtil.getFieldValue(token, "https://morningstar.com/mstar_id");
        return StringUtils.defaultIfEmpty(tokenUserId, headerUserId);

    }

    private static String getConfigIdFromToken(String token) {

        return JwtUtil.getFieldValue(token, "https://morningstar.com/config_id");

    }

    private com.morningstar.martapi.grpc.TimeSeriesResponse convertToTimeSeriesResponse(InvestmentResponse investmentResponse) {
        com.morningstar.martapi.grpc.TimeSeriesResponse.Builder builder = com.morningstar.martapi.grpc.TimeSeriesResponse.newBuilder();

        // Convert status
//        if (investmentResponse.getStatus() != null) {
//            StatusProto statusProto = StatusProto.newBuilder()
//                    .setCode(investmentResponse.getStatus().getCode() != null ? investmentResponse.getStatus().getCode() : "")
//                    .setMessage(investmentResponse.getStatus().getMessage() != null ? investmentResponse.getStatus().getMessage() : "")
//                    .build();
//            builder.setStatus(statusProto);
//        }

        // Convert investments
        if (investmentResponse.getInvestments() != null) {
            for (Investment investment : investmentResponse.getInvestments()) {
                com.morningstar.martapi.grpc.Investment.Builder investmentBuilder = com.morningstar.martapi.grpc.Investment.newBuilder()
                        .setId(investment.getId());

                if (investment.getTimeseriesDataList() != null) {
                    for (TimeseriesData timeseriesData : investment.getTimeseriesDataList()) {
                        if (timeseriesData.getTimeseriesPairList() != null) {

                            com.morningstar.martapi.grpc.DataPoint.Builder investmentValueBuilder = com.morningstar.martapi.grpc.DataPoint.newBuilder()
                                    .setDatapointId(timeseriesData.getDatapointId());

                            for (TimeseriesPair timeseriesPair : timeseriesData.getTimeseriesPairList()) {

                                com.morningstar.martapi.grpc.TimeSeriesPoint.Builder timeSeriesDataPointBuilder = com.morningstar.martapi.grpc.TimeSeriesPoint.newBuilder()
                                        .setDate(timeseriesPair.getDate())
                                        .setValue(timeseriesPair.getValue());
                                investmentValueBuilder.addTimeSeriesData(timeSeriesDataPointBuilder.build());
                            }

                            investmentBuilder.addValues(investmentValueBuilder.build());
                        }
                    }
                }

                builder.addInvestments(investmentBuilder.build());
            }
        }

        return builder.build();
    }

    private com.morningstar.martapi.grpc.OldTimeSeriesResponse convertToOldTimeSeriesResponse(TSResponse tsResponse) {
        com.morningstar.martapi.grpc.OldTimeSeriesResponse.Builder builder = com.morningstar.martapi.grpc.OldTimeSeriesResponse.newBuilder();

        if (tsResponse.getContent() != null && tsResponse.getContent().getItems() != null) {
            com.morningstar.martapi.grpc.Status.Builder statusBuilder = com.morningstar.martapi.grpc.Status.newBuilder()
                    .setCode(tsResponse.getStatus().getCode())
                    .setMsg(tsResponse.getStatus().getMsg());
            builder.setS(statusBuilder.build());
            com.morningstar.martapi.grpc.Content.Builder contentBuilder = com.morningstar.martapi.grpc.Content.newBuilder();
            builder.setC(contentBuilder.build());

            for (TSItem item : tsResponse.getContent().getItems()) {
                com.morningstar.martapi.grpc.Item.Builder itemBuilder = com.morningstar.martapi.grpc.Item.newBuilder()
                        .setSecid(item.getSecid())
                        .setDataid(item.getDataid());

                item.getData().forEach(data -> {
                    com.morningstar.martapi.grpc.DataPointValue.Builder dataPointValueBuilder = com.morningstar.martapi.grpc.DataPointValue.newBuilder()
                            .setD(data.getDate())
                            .setV(data.getValue());
                    itemBuilder.addData(dataPointValueBuilder.build());
                });
                contentBuilder.addI(itemBuilder.build());
            }
            builder.setC(contentBuilder.build());
        }

        return builder.build();
    }

    private void logSuccess(MartRequest martRequest, long startTime) {
        MDC.put(LogAttribute.REQUEST_ID.getDisplayName(), martRequest.getRequestId());
        long executionTime = System.currentTimeMillis() - startTime;
        List<LogEntity> logEntities = Stream.of(
                new LogEntity(EVENT_TYPE, RESPONSE),
                new LogEntity(EVENT_DESCRIPTION, SUCCESSFUL_RESPONSE),
                new LogEntity(REQUEST_TYPE, REQUEST_TYPE_VALUE),
                new LogEntity(PRODUCT_ID, martRequest.getProductId()),
                new LogEntity(USER_ID, martRequest.getUserId()),
                new LogEntity(EXECUTE_TIME, executionTime)
        ).collect(Collectors.toCollection(ArrayList::new));
        addRequestPayload(martRequest, executionTime, logEntities);
        LogEntry.info(logEntities.toArray(LogEntity[]::new));
    }

    private void logError(MartRequest martRequest, long startTime, Throwable error) {
        MDC.put(LogAttribute.REQUEST_ID.getDisplayName(), martRequest.getRequestId());
        LogEntry.error(
                new LogEntity(EVENT_TYPE, RESPONSE_ERROR),
                new LogEntity(EVENT_DESCRIPTION, error),
                new LogEntity(REQUEST_TYPE, REQUEST_TYPE_VALUE),
                new LogEntity(REQUEST_PAYLOAD, JsonUtils.toJsonString(martRequest)),
                new LogEntity(EXECUTE_TIME, System.currentTimeMillis() - startTime),
                new LogEntity(EXCEPTION_TYPE, error.getClass()),
                new LogEntity(PRODUCT_ID, martRequest.getProductId()),
                new LogEntity(USER_ID, martRequest.getUserId())
        );
    }

    private void addRequestPayload(MartRequest martRequest, long executionTime, List<LogEntity> logEntities) {
        if (executionTime > LOG_REQUEST_PAYLOAD_LIMIT_MILLISECONDS) {
            logEntities.add(new LogEntity(REQUEST_PAYLOAD, JsonUtils.toJsonString(martRequest)));
        }
    }

    private <T> void handleAsyncRetrieveSecurities(MartRequest martRequest, long startTime, StreamObserver<T> responseObserver, Function<TSResponse, T> responseConverter) {
        tsOldRspGateway.asyncRetrieveSecurities(martRequest)
                .subscribe(
                        result -> {
                            T response = responseConverter.apply(result);
                            responseObserver.onNext(response);
                            responseObserver.onCompleted();
                            logSuccess(martRequest, startTime);
                        },
                        error -> {
                            responseObserver.onError(error);
                            logError(martRequest, startTime, error);
                        }
                );
    }

    private void handleException(Exception e, MartRequest martRequest, long startTime, StreamObserver<?> responseObserver) {
        logError(martRequest, startTime, e);
        io.grpc.Status status;
        if (e instanceof TsCacheProtobufValidationException) {
            status = io.grpc.Status.INVALID_ARGUMENT.withDescription(e.getMessage());
        } else if (e instanceof ValidationException) {
            status = io.grpc.Status.INVALID_ARGUMENT.withDescription(e.getMessage());
        } else {
            status = io.grpc.Status.INTERNAL.withDescription(e.getMessage());
        }
        responseObserver.onError(status.asRuntimeException());
    }
}
