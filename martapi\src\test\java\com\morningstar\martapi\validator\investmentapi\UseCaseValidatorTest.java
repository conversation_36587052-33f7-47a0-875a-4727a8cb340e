package com.morningstar.martapi.validator.investmentapi;

import com.morningstar.martapi.exception.InvestmentApiValidationException;
import com.morningstar.martgateway.domains.core.entity.response.Status;
import com.morningstar.martcommon.entity.payload.gridview.GridviewDataPoint;
import com.morningstar.martgateway.interfaces.model.investmentapi.Investment;
import com.morningstar.martgateway.interfaces.model.investmentapi.InvestmentApiRequest;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;

import java.util.List;

public class UseCaseValidatorTest {

    private UseCaseValidator validator;

    @Before
    public void setup() {
        this.validator = new UseCaseValidator<>(InvestmentApiValidationException::new);
    }

    @Test
    public void validate() {
        InvestmentApiRequest request = InvestmentApiRequest.builder()
                .useCase("unknown")
                .investments(List.of(Investment.builder().id("OP01010").build(),Investment.builder().id("OP01012").build()))
                .dataPoints(List.of(GridviewDataPoint.builder().dataPointId("HP010").build(), GridviewDataPoint.builder().dataPointId("HP022").build()))
                .build();
        InvestmentApiValidationException exception = Assertions.assertThrows(InvestmentApiValidationException.class,
                () -> validator.validate(request));
        Assertions.assertEquals(Status.INVALID_USE_CASE.getCode(),exception.getStatus().getCode());
    }
}
