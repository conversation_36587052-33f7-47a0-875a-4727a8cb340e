package com.morningstar.martapi.validator.tsrequest;

import com.morningstar.martapi.exception.ValidationException;
import com.morningstar.martapi.validator.Validator;
import com.morningstar.martcommon.entity.result.request.MartRequest;
import com.morningstar.martgateway.domains.core.entity.response.Status;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import org.apache.commons.lang3.StringUtils;

public class TsMartRequestDateValidator implements Validator<MartRequest> {

	private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

	@Override
	public void validate(MartRequest request) throws RuntimeException {
		if (!isWithoutDateRange(request) && (isMissingDate(request) || isInvalidDateOrder(request))) {
				throw new ValidationException(Status.INVALID_DATE_SETTING);
		}
	}

	private boolean isInvalidDateOrder(MartRequest request) {
		try {
			LocalDate startDate = LocalDate.parse(request.getStartDate(), formatter);
			LocalDate endDate = LocalDate.parse(request.getEndDate(), formatter);
			return startDate.isAfter(endDate);
		} catch (DateTimeParseException e) {
			return true;
		}
	}

	private boolean isMissingDate(MartRequest request) {
		String startDate = request.getStartDate();
		String endDate = request.getEndDate();
		return (StringUtils.isEmpty(startDate) && StringUtils.isNotEmpty(endDate)) ||
				(StringUtils.isNotEmpty(startDate) && StringUtils.isEmpty(endDate));
	}

	private boolean isWithoutDateRange(MartRequest request) {
		return StringUtils.isEmpty(request.getStartDate()) && StringUtils.isEmpty(request.getEndDate());
	}
}
