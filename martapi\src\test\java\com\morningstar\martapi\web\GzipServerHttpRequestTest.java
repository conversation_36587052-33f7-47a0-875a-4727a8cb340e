package com.morningstar.martapi.web;

import com.amazonaws.util.IOUtils;
import com.morningstar.martapi.exception.IllegalGzipRequestException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DefaultDataBufferFactory;
import org.springframework.http.HttpCookie;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.server.RequestPath;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.SslInfo;
import org.springframework.util.MultiValueMap;
import reactor.core.publisher.Flux;
import reactor.test.StepVerifier;

import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.InetSocketAddress;
import java.net.URI;
import java.nio.charset.StandardCharsets;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class GzipServerHttpRequestTest {

    private ServerHttpRequest originalRequest;
    private GzipServerHttpRequest gzipRequest;
    private SslInfo sslInfo;

    @BeforeEach
    public void setUp() {
        originalRequest = mock(ServerHttpRequest.class);
        sslInfo = mock(SslInfo.class);
        gzipRequest = new GzipServerHttpRequest(originalRequest);
    }

    @Test
    public void testGetIdDelegatesToOriginalRequest() {
        when(originalRequest.getId()).thenReturn("someId");
        assertEquals("someId", gzipRequest.getId());
        verify(originalRequest).getId();
    }

    @Test
    public void testGetPathDelegatesToOriginalRequest() {
        RequestPath mockPath = mock(RequestPath.class);
        when(originalRequest.getPath()).thenReturn(mockPath);
        assertEquals(mockPath, gzipRequest.getPath());
        verify(originalRequest).getPath();
    }

    @Test
    public void testGetQueryParamsDelegatesToOriginalRequest() {
        MultiValueMap<String, String> mockQueryParams = mock(MultiValueMap.class);
        when(originalRequest.getQueryParams()).thenReturn(mockQueryParams);
        assertEquals(mockQueryParams, gzipRequest.getQueryParams());
        verify(originalRequest).getQueryParams();
    }

    @Test
    public void testGetCookiesDelegatesToOriginalRequest() {
        MultiValueMap<String, HttpCookie> mockCookies = mock(MultiValueMap.class);
        when(originalRequest.getCookies()).thenReturn(mockCookies);
        assertEquals(mockCookies, gzipRequest.getCookies());
        verify(originalRequest).getCookies();
    }

    @Test
    public void testGetMethodValueDelegatesToOriginalRequest() {
        when(originalRequest.getMethodValue()).thenReturn("POST");
        assertEquals("POST", gzipRequest.getMethodValue());
        verify(originalRequest).getMethodValue();
    }

    @Test
    public void testGetURIDelegatesToOriginalRequest() {
        URI mockURI = URI.create("http://morningstar.com");
        when(originalRequest.getURI()).thenReturn(mockURI);
        assertEquals(mockURI, gzipRequest.getURI());
        verify(originalRequest).getURI();
    }

    @Test
    public void testGetHeadersDelegatesToOriginalRequest() {
        HttpHeaders httpHeaders = new HttpHeaders();
        when(originalRequest.getHeaders()).thenReturn(httpHeaders);
        assertEquals(httpHeaders, gzipRequest.getHeaders());
        verify(originalRequest).getHeaders();
    }

    @Test
    public void testGetRemoteAddressDelegatesToOriginalRequest() {
        InetSocketAddress mockAddress = new InetSocketAddress("localhost", 8080);
        when(originalRequest.getRemoteAddress()).thenReturn(mockAddress);
        assertEquals(mockAddress, gzipRequest.getRemoteAddress());
        verify(originalRequest).getRemoteAddress();
    }

    @Test
    void getSslInfoDelegatesToOriginalRequest() {
        when(originalRequest.getSslInfo()).thenReturn(sslInfo);
        GzipServerHttpRequest gzipRequest = new GzipServerHttpRequest(originalRequest);

        SslInfo result = gzipRequest.getSslInfo();

        assertSame(sslInfo, result);
        verify(originalRequest).getSslInfo();
    }

    @Test
    public void testGetMethodDelegatesToOriginalRequest() {
        when(originalRequest.getMethod()).thenReturn(HttpMethod.POST);
        assertEquals(HttpMethod.POST, gzipRequest.getMethod());
        verify(originalRequest).getMethod();
    }

    @Test
    public void testGetBodyDecompressesData() throws IOException {
        InputStream gzipInputStream = getClass().getResourceAsStream("/gzip/gridviewTimeSeriesExample.json.gz");

        DefaultDataBufferFactory dataBufferFactory = new DefaultDataBufferFactory();
        ByteArrayOutputStream buffer = new ByteArrayOutputStream();
        byte[] data = new byte[1024];
        int nRead;
        while ((nRead = gzipInputStream.read(data, 0, data.length)) != -1) {
            buffer.write(data, 0, nRead);
        }
        byte[] compressedContent = buffer.toByteArray();
        DataBuffer dataBuffer = dataBufferFactory.wrap(compressedContent);

        ServerHttpRequest originalRequest = mock(ServerHttpRequest.class);
        when(originalRequest.getBody()).thenReturn(Flux.just(dataBuffer));

        GzipServerHttpRequest gzipServerHttpRequest = new GzipServerHttpRequest(originalRequest);

        StepVerifier.create(gzipServerHttpRequest.getBody())
                .expectNextMatches(db -> {
                    StringBuilder jsonContent = new StringBuilder();
                    try (BufferedReader br = new BufferedReader(new InputStreamReader(db.asInputStream(), StandardCharsets.UTF_8))) {
                        String line;
                        while ((line = br.readLine()) != null) {
                            jsonContent.append(line);
                            jsonContent.append(System.lineSeparator());
                        }
                        assertEquals(getJsonStringFromResourceFile("/gzip/gridviewTimeSeriesExample.json").trim(), jsonContent.toString().trim());
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                    return true;
                })
                .verifyComplete();
    }

    private String getJsonStringFromResourceFile(String filePath) throws IOException {
        return IOUtils.toString(new ClassPathResource(filePath).getInputStream());
    }

    @Test
    void decompressHandlesIOException() throws IOException {
        InputStream throwingInputStream = mock(InputStream.class);
        when(throwingInputStream.read(any(byte[].class))).thenThrow(IOException.class);

        DataBuffer dataBuffer = mock(DataBuffer.class);
        when(dataBuffer.asInputStream(anyBoolean())).thenReturn(throwingInputStream);

        when(originalRequest.getBody()).thenReturn(Flux.just(dataBuffer));

        GzipServerHttpRequest gzipRequest = new GzipServerHttpRequest(originalRequest);

        StepVerifier.create(gzipRequest.getBody())
                .expectError(IllegalGzipRequestException.class)
                .verify();
    }


}