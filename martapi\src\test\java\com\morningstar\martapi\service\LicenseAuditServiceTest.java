package com.morningstar.martapi.service;

import com.morningstar.dataac.martgateway.core.entitlement.entity.LicenseCellResponse;
import com.morningstar.dataac.martgateway.core.entitlement.entity.PackageEntitlementResponse;
import com.morningstar.martapi.entity.LicenseAuditEntity;
import com.morningstar.martapi.entity.LicenseAuditResponse;
import com.morningstar.martapi.entity.LicenseCellEntity;
import com.morningstar.dataac.martgateway.core.entitlement.service.DataEntitlementService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class LicenseAuditServiceTest {

    LicenseAuditService licenseAuditService;
    DataEntitlementService dataEntitlementService;
    @Before
    public void setup() {
        dataEntitlementService = mock(DataEntitlementService.class);
        licenseAuditService = new LicenseAuditService(dataEntitlementService);
    }

    @Test
    public void testProcessAudit() {
        PackageEntitlementResponse pckgResponse = new PackageEntitlementResponse(new HashMap<>(), Arrays.asList("dp2"));
        pckgResponse.setUnidentifiedInvestments(Arrays.asList("inv3"));
        when(dataEntitlementService.getLicenseAudit(any(), any(), any(), any(), any(), anyBoolean())).thenReturn(pckgResponse);

        LicenseAuditEntity request = new LicenseAuditEntity(new HashSet<>(Arrays.asList("dp1")), new HashSet<>(Arrays.asList("inv1", "inv2", "inv3")));
        LicenseAuditResponse response = licenseAuditService.processAudit("user1", "product1", null, request, false);
        Assert.assertNotNull(response);
        Assert.assertEquals(1, response.getUnidentifiedInvestments().size());
        Assert.assertEquals(1, response.getUnidentifiedDataPoints().size());
    }

    @Test
    public void testProcessCellInspection() {
        when(dataEntitlementService.getLicenseCellInspection("user1", "config1","product1","dp1","inv1",null,"feed")).thenReturn(new LicenseCellResponse());
        LicenseCellEntity request = LicenseCellEntity.builder().dataPointId("dp1").investmentId("inv1").build();
        request.setUseCase("feed");
        Assert.assertNotNull(licenseAuditService.processCellInspection("user1", "config1","product1", request));

        request.setUseCase("invalid_usecase");
        Assert.assertNotNull(licenseAuditService.processCellInspection("user1", "config1", "product1", request));
    }
}
