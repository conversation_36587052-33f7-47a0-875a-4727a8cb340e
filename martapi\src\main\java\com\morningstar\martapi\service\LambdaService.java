package com.morningstar.martapi.service;

import com.amazonaws.services.lambda.AWSLambda;
import com.amazonaws.services.lambda.model.InvokeRequest;
import com.amazonaws.services.lambda.model.InvokeResult;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.morningstar.martgateway.domains.core.entity.MartException;
import com.morningstar.martgateway.interfaces.model.AsyncInput.ApiType;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;
import java.time.Instant;

@Slf4j
public class LambdaService {

    private final ObjectMapper objectMapper;

    private static final Logger LOGGER = LoggerFactory.getLogger(LambdaService.class);
    private final AWSLambda awsLambda;
    private final String functionName;

    public LambdaService(AWSLambda awsLambda, String functionName) {
        this.awsLambda = awsLambda;
        this.functionName = functionName;
        this.objectMapper = new ObjectMapper();
    }

    public LambdaService(AWSLambda awsLambda, String functionName, ObjectMapper objectMapper) {
        this.awsLambda = awsLambda;
        this.functionName = functionName;
        this.objectMapper = objectMapper;
    }

    public void invokeLambda(String jobId, ApiType apiType) {
        Map<String, String> payloadMap = Map.of("apiType", getApiType(apiType));

        Instant instant = Instant.now();
        InvokeRequest invokeRequest = new InvokeRequest();
        invokeRequest.setPayload(toJson(payloadMap));
        invokeRequest.setFunctionName(functionName);
        invokeRequest.setInvocationType("Event");
        InvokeResult invokeResult = awsLambda.invoke(invokeRequest);
        LOGGER.info("Invoke Scaling Lambda for job {} with status_code: {}, aws_request_id: {}, total time: {}ms",
                jobId, invokeResult.getStatusCode(), invokeResult.getSdkResponseMetadata().getRequestId(), Duration.between(instant, Instant.now()).toMillis());
    }

    private String getApiType(ApiType apiType) {
        return apiType == ApiType.INVESTMENT_API ? "gridview" : "ph";
    }

    private String toJson(Map<String, String> payloadMap) {
        try {
            return objectMapper.writeValueAsString(payloadMap);
        } catch (JsonProcessingException e) {
            String msg = String.format("Error writing payload for async lambda function with payload [%s]", payloadMap.toString());
            log.error(msg, e);
            throw new MartException(msg, e);
        }
    }
}
