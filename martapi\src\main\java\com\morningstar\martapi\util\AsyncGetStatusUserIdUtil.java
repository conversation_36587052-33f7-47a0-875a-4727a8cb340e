package com.morningstar.martapi.util;

import com.morningstar.martgateway.util.JwtUtil;
import com.mysql.cj.util.StringUtils;
import java.util.Set;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@NoArgsConstructor
public class AsyncGetStatusUserIdUtil {

    private String serviceAccountUimRole = "INVESTMENTAPI_INTERNAL_SERVICE_ACCOUNT";

    public AsyncGetStatusUserIdUtil(String serviceAccountUimRole) {
        this.serviceAccountUimRole = serviceAccountUimRole;
    }

    public String determineUserId(String bearerToken, String headerUserId) {
        String tokenUserId = JwtUtil.getFieldValue(bearerToken, "https://morningstar.com/mstar_id");
        if (StringUtils.isNullOrEmpty(headerUserId)) {
            return tokenUserId;
        }
        String fieldValue = JwtUtil.getFieldValue(bearerToken, "https://morningstar.com/uim_roles");
        Set<String> uimRoles = StringUtils.isNullOrEmpty(fieldValue) ? Set.of() : Set.of(fieldValue.split(","));
        return uimRoles.contains(serviceAccountUimRole) ? headerUserId : tokenUserId;
    }
}
