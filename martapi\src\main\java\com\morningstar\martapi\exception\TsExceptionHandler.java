package com.morningstar.martapi.exception;

import static com.morningstar.martapi.exception.utils.ExceptionHandlerUtils.extractProductId;
import static com.morningstar.martapi.exception.utils.ExceptionHandlerUtils.extractStatusCode;

import com.morningstar.dataac.martgateway.core.entitlement.exception.EntitlementException;
import com.morningstar.martapi.controller.TimeSeriesController;
import com.morningstar.martgateway.domains.core.entity.investmentresponse.InvestmentResponse;
import com.morningstar.martgateway.domains.core.entity.response.Status;
import com.morningstar.martgateway.domains.timeseries.entity.TSResponse;
import com.morningstar.martgateway.domains.timeseries.entity.TSStatus;
import com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.server.ServerWebExchange;

@Order(Ordered.HIGHEST_PRECEDENCE)
@RestControllerAdvice(assignableTypes = {TimeSeriesController.class})
public class TsExceptionHandler {

	private static final Logger LOGGER = LoggerFactory.getLogger(TsExceptionHandler.class);

	@ExceptionHandler(value = TsGridViewValidationException.class)
	public ResponseEntity<InvestmentResponse> handleValidationException(
			TsGridViewValidationException e,
			ServerWebExchange ex) {
		String url = ex.getRequest().getURI().toString();
		String productId = extractProductId(ex);
		LOGGER.warn("event_type=\"TimeSeries GridView API Validation\", " +
				"event_description=\"Validation failure\", " +
				"url=\"{}\", product_id=\"{}\", e=\"{}\"", url, productId, e.getMessage());
		return ResponseEntity
				.status(extractStatusCode(e.getStatus()))
				.body(new InvestmentResponse(e.getStatus(), null));
	}
	@ExceptionHandler(value = EntitlementException.class)
	public ResponseEntity<?> handleEntitlementException(
			EntitlementException e,
			ServerWebExchange ex) {
		String url = ex.getRequest().getURI().toString();
		String format = ex.getRequest().getQueryParams().getFirst("format");
		String productId = extractProductId(ex);

		if ("0".equals(format)) {
			logAuthFailure("TimeSeries Protobuf API", url, productId, e.getMessage());
			return ResponseEntity
					.status(extractStatusCode(e.getStatus()))
					.body(createTsResponse(e.getStatus()).toProtobuf());
		} else if ("1".equals(format)) {
			logAuthFailure("TimeSeries TSCache API", url, productId, e.getMessage());
			return ResponseEntity
					.status(extractStatusCode(e.getStatus()))
					.body(createTsResponse(e.getStatus()));
		} else if ("json".equals(format)){
			logAuthFailure("TimeSeries GridView API", url, productId, e.getMessage());
			return ResponseEntity
					.status(extractStatusCode(e.getStatus()))
					.body(new InvestmentResponse(e.getStatus(), null));
		} else {
			return ResponseEntity
					.status(extractStatusCode(e.getStatus()))
					.body(e.getStatus());
		}
	}

	@ExceptionHandler(value = TsCacheApiValidationException.class)
	public ResponseEntity<TSResponse> handleValidationException(
			TsCacheApiValidationException e,
			ServerWebExchange ex) {
		String url = ex.getRequest().getURI().toString();
		String productId = extractProductId(ex);
		LOGGER.warn("event_type=\"TimeSeries TSCache API Validation\", " +
				"event_description=\"Validation failure\", " +
				"url=\"{}\", product_id=\"{}\", e=\"{}\"", url, productId, e.getMessage());
		return ResponseEntity
				.status(extractStatusCode(e.getStatus()))
				.body(createTsResponse(e.getStatus()));
	}

	private TSResponse createTsResponse(Status status) {
		TSStatus tsStatus = new TSStatus();
		if (status != null) {
			tsStatus.setCode(status.getCode());
			tsStatus.setMsg(status.getMessage());
		}
		return new TSResponse(tsStatus);
	}

	private void logAuthFailure(String apiType, String url, String productId, String errorMessage) {
		LOGGER.warn("event_type=\"{} Authorization Failure\", " +
						"event_description=\"Entitlement retrieval failed\", " +
						"url=\"{}\", product_id=\"{}\", e=\"{}\"",
				apiType, url, productId, errorMessage);
	}

	@ExceptionHandler(value = TsCacheProtobufValidationException.class)
	public ResponseEntity<TsCacheDataForProtoBuf.TimeSeriesDatas> handleValidationException(
			TsCacheProtobufValidationException e,
			ServerWebExchange ex) {
		String url = ex.getRequest().getURI().toString();
		String productId = extractProductId(ex);
		LOGGER.warn("event_type=\"TimeSeries Protobuf API Validation\", " +
				"event_description=\"Validation failure\", " +
				"url=\"{}\", product_id=\"{}\", e=\"{}\"", url, productId, e.getMessage());
		return ResponseEntity
				.status(extractStatusCode(e.getStatus()))
				.body(createTsResponse(e.getStatus()).toProtobuf());
	}

}
