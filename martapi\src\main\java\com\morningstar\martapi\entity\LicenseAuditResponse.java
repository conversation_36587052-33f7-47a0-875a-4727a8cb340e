package com.morningstar.martapi.entity;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.morningstar.dataac.martgateway.core.entitlement.entity.PackageDetail;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LicenseAuditResponse {
    private List<PackageDetail> dataPackages;
    private List<String> unidentifiedDataPoints;
    private List<String> unidentifiedInvestments;
}
