package com.morningstar.martapi.config;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.web.reactive.accept.RequestedContentTypeResolverBuilder;

import static org.mockito.Mockito.when;
@RunWith(MockitoJUnitRunner.class)
public class WebFluxConfigTest {
    private WebFluxConfig webFluxConfig;
    @Mock
    private RequestedContentTypeResolverBuilder builder;

    @Test
    public void shouldCustomizeTest() {
        webFluxConfig = new WebFluxConfig();
        RequestedContentTypeResolverBuilder.ParameterResolverConfigurer  parameterResolverConfigurer = new RequestedContentTypeResolverBuilder.ParameterResolverConfigurer();
        when(builder.parameterResolver()).thenReturn(parameterResolverConfigurer);
        webFluxConfig.configureContentTypeResolver(builder);
    }
}
