package com.morningstar.martapi.exception;

import com.morningstar.martgateway.domains.core.entity.investmentresponse.InvestmentResponse;
import com.morningstar.martgateway.domains.core.entity.response.Status;
import com.morningstar.dataac.martgateway.core.entitlement.exception.EntitlementException;
import junit.framework.TestCase;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.core.codec.DecodingException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.server.ServerWebExchange;

import java.net.URI;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class LicenseExceptionHandlerTest {
    private LicenseExceptionHandler handler;
    @Mock
    private ServerWebExchange serverWebExchange;
    @Mock
    private ServerHttpRequest httpRequest;


    @BeforeEach
    void setup() {
        handler = new LicenseExceptionHandler();

        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.addAll("X-Api-ProductId", List.of("MDS"));
        when(serverWebExchange.getRequest()).thenReturn(httpRequest);
        when(httpRequest.getURI()).thenReturn(URI.create("test1"));
        when(httpRequest.getHeaders()).thenReturn(httpHeaders);
    }

    @Test
    public void handleValidationException() {
        ValidationException v = new ValidationException(Status.MISSING_ATTRIBUTE.withMessage("Request input missing mandatory attribute - Investment list"));
        ResponseEntity<Status> response = (ResponseEntity<Status>) handler.handleValidationException(
                v, serverWebExchange);
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertEquals(response.getBody().getMessage(), "Request input missing mandatory attribute - Investment list");
    }

    @Test
    public void handleEntitlementExceptionTest() {
        EntitlementException e = new EntitlementException(Status.REDIS_CONNECTION_FAILED, null);
        ResponseEntity<Status> response = (ResponseEntity<Status>) handler.handleEntitlementException(
                e, serverWebExchange);
        TestCase.assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        TestCase.assertEquals(response.getBody().getCode(), "500300");
        TestCase.assertEquals(response.getBody().getMessage(), "Redis connection failed");
    }

    @Test
    public void handleOtherException() {
        Exception e = new Exception("Internal Server error");
        ResponseEntity<Status> response = (ResponseEntity<Status>) handler.handleOtherException(e, serverWebExchange);
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertEquals(response.getBody().getMessage(), "Internal Error");
    }

    @Test
    public void handleInvalidRequestInputTest() {
        ResponseEntity<InvestmentResponse> responseEntity = handler.handleInvalidRequestInput(new DecodingException("JSON error"), serverWebExchange);
        assertEquals(HttpStatus.BAD_REQUEST,responseEntity.getStatusCode());
    }
}
