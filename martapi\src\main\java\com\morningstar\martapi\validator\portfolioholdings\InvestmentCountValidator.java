package com.morningstar.martapi.validator.portfolioholdings;


import com.morningstar.dataac.martgateway.data.ph.entity.HoldingDataRequest;
import com.morningstar.martapi.exception.HoldingValidationException;
import com.morningstar.martapi.validator.Validator;
import com.morningstar.martgateway.domains.core.entity.response.Status;

public class InvestmentCountValidator implements Validator<HoldingDataRequest> {

    @Override
    public void validate(HoldingDataRequest request) throws RuntimeException {
        if (request.getInvestments().size() != 1) {
            throw new HoldingValidationException(Status.BAD_REQUEST.withMessage("Exactly one investment identifier is required"));
        }
    }
}
