package com.morningstar.martapi.controller;

import com.morningstar.martapi.util.InvestmentApiRequestUtil;
import com.morningstar.martapi.validator.RequestValidationHandler;
import com.morningstar.martapi.validator.entity.HeadersAndParams;
import com.morningstar.martgateway.domains.core.entity.investmentresponse.InvestmentResponse;
import com.morningstar.martgateway.interfaces.MartGateway;
import com.morningstar.martgateway.interfaces.model.investmentapi.InvestmentApiRequest;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@RestController
@RequestMapping(value = "/investment-api/v1/delta-detection", produces = MediaType.APPLICATION_JSON_VALUE)
public class DeltaController {

    private final MartGateway<InvestmentResponse, InvestmentApiRequest> deltaDetectionGateway;
    private final RequestValidationHandler<HeadersAndParams,InvestmentApiRequest> deltaDetectionApiValidator;

    public DeltaController(
            @Qualifier("deltaGatewayImpl") MartGateway<InvestmentResponse, InvestmentApiRequest> deltaDetectionGateway,
            @Qualifier("deltaDetectionApiValidator") RequestValidationHandler<HeadersAndParams,InvestmentApiRequest> deltaDetectionApiValidator
    ) {
        this.deltaDetectionGateway = deltaDetectionGateway;
        this.deltaDetectionApiValidator = deltaDetectionApiValidator;
    }

    @PostMapping(consumes = MediaType.APPLICATION_JSON_VALUE)
    public Mono<InvestmentResponse> getDeltaDetection(
            @RequestBody InvestmentApiRequest investmentApiRequest,
            @RequestHeader(value = "Authorization", required = false, defaultValue = "") String token,
            @RequestHeader(value = "X-API-RequestId", required = false, defaultValue = "") String requestId,
            @RequestHeader(value = "X-API-ProductId", required = false, defaultValue = "") String productId) {
        investmentApiRequest.setDeltaDetection(true);
        InvestmentApiRequestUtil investmentApiRequestUtil = new InvestmentApiRequestUtil();
        HeadersAndParams headersAndParams = investmentApiRequestUtil.getHeaderAndParams(token, productId, requestId);
        deltaDetectionApiValidator.validateHeadersAndParams(headersAndParams);
        deltaDetectionApiValidator.validateRequestBody(investmentApiRequest);
        return deltaDetectionGateway.asyncRetrieveSecurities(investmentApiRequest);
    }
}
