package com.morningstar.martapi.validator;

import com.morningstar.martapi.exception.InvestmentApiValidationException;
import com.morningstar.martapi.validator.entity.HeadersAndParams;
import com.morningstar.martgateway.domains.core.entity.response.Status;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;

public class RequestIdValidatorTest {


    @Test
    public void validate() {
        RequestIdValidator validator = new RequestIdValidator();
        HeadersAndParams headersAndParams = HeadersAndParams.builder().requestId(UUID.randomUUID().toString()).build();
        assertDoesNotThrow(() -> validator.validate(headersAndParams));
    }

    @Test
    public void validateEmptyRequestId() {
        RequestIdValidator validator = new RequestIdValidator();
        HeadersAndParams headersAndParams = HeadersAndParams.builder().build();
        InvestmentApiValidationException exception = Assertions.assertThrows(InvestmentApiValidationException.class,
                () -> validator.validate(headersAndParams));
        Assertions.assertEquals(Status.INVALID_REQUEST_ID.getCode(),exception.getStatus().getCode());
    }

    @Test
    public void validateUuid() {
        RequestIdValidator validator = new RequestIdValidator();
        HeadersAndParams headersAndParams = HeadersAndParams.builder().requestId("Not a UUID").build();
        InvestmentApiValidationException exception = Assertions.assertThrows(InvestmentApiValidationException.class,
                () -> validator.validate(headersAndParams));
        Assertions.assertEquals(Status.INVALID_REQUEST_ID.getCode(),exception.getStatus().getCode());
    }
}
