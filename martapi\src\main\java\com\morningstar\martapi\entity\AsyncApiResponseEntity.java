package com.morningstar.martapi.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.morningstar.martgateway.interfaces.model.AsyncInput;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AsyncApiResponseEntity {
    private String jobStatus;
    private String jobId;
    private String productId;
    private String startTime;
    private String endTime;
    private String urlExpireTime;
    private String url;
    private String errorMessage;
    @JsonIgnore
    private AsyncInput.ApiType apiType;
    private String totalPages;
    @JsonIgnore
    private String s3regionOfData;
    List<S3Url> s3Urls;
}
