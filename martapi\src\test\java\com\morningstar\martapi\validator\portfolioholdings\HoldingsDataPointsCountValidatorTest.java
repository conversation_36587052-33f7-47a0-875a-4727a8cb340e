package com.morningstar.martapi.validator.portfolioholdings;

import com.morningstar.dataac.martgateway.data.ph.entity.HoldingDataPoint;
import com.morningstar.dataac.martgateway.data.ph.entity.PortfolioSetting;
import com.morningstar.martapi.exception.HoldingValidationException;
import com.morningstar.dataac.martgateway.data.ph.entity.HoldingDataRequest;

import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertThrows;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;

public class HoldingsDataPointsCountValidatorTest {

    @Test
    public void testValidate_DataPointsBelowLimit() {
        HoldingDataRequest request = createRequestWithDataPoints(500);
        HoldingsDataPointsCountValidator validator = new HoldingsDataPointsCountValidator();
        assertDoesNotThrow(() -> validator.validate(request));
    }

    @Test
    public void testValidate_DataPointsAtLimit() {
        HoldingDataRequest request = createRequestWithDataPoints(1000);
        HoldingsDataPointsCountValidator validator = new HoldingsDataPointsCountValidator();
        assertDoesNotThrow(() -> validator.validate(request));
    }

    @Test
    public void testValidate_DataPointsAboveLimit() {
        HoldingDataRequest request = createRequestWithDataPoints(1001);
        HoldingsDataPointsCountValidator validator = new HoldingsDataPointsCountValidator();
        HoldingValidationException exception = assertThrows(
                HoldingValidationException.class,
                () -> validator.validate(request)
        );
        assertEquals("A maximum of 1000 data points are allowed", exception.getMessage());
        assertEquals("400", exception.getStatus().getCode().substring(0, 3));
    }

    @Test
    public void testValidate_EmptyHoldingsDataPoints() {
        HoldingDataRequest request = createRequestWithDataPoints(0);
        HoldingsDataPointsCountValidator validator = new HoldingsDataPointsCountValidator();
        assertDoesNotThrow(() -> validator.validate(request));
    }

    private HoldingDataRequest createRequestWithDataPoints(int numberOfDataPoints) {
        HoldingDataRequest request = new HoldingDataRequest();
        PortfolioSetting portfolioSetting = new PortfolioSetting();
        List<HoldingDataPoint> dataPointsList = new ArrayList<>();

        for (int i = 0; i < numberOfDataPoints; i++) {
            HoldingDataPoint dataPoint = new HoldingDataPoint();
            dataPoint.setDataPointId("dataPointId" + i);
            dataPoint.setAlias("alias" + i);
            dataPoint.setCurrency("USD");
            dataPointsList.add(dataPoint);
        }

        portfolioSetting.setHoldingsDataPoints(dataPointsList);
        request.setPortfolioSetting(portfolioSetting);
        return request;
    }

}