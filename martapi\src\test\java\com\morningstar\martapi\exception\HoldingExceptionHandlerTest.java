package com.morningstar.martapi.exception;

import static junit.framework.TestCase.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.morningstar.dataac.martgateway.data.ph.exception.PortfolioHoldingException;
import com.morningstar.martgateway.domains.core.entity.investmentresponse.InvestmentResponse;
import com.morningstar.martgateway.domains.core.entity.response.Status;
import com.morningstar.dataac.martgateway.core.entitlement.exception.EntitlementException;

import java.lang.reflect.Method;
import java.net.URI;
import java.util.List;
import java.util.regex.Matcher;

import org.junit.Before;
import org.junit.Test;
import org.springframework.core.codec.DecodingException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.ServerWebInputException;

public class HoldingExceptionHandlerTest {

    private HoldingExceptionHandler handler = new HoldingExceptionHandler();

    private ServerWebExchange serverWebExchange;

    @Before
    public void setup() {
        serverWebExchange = mock(ServerWebExchange.class);
        ServerHttpRequest httpRequest = mock(ServerHttpRequest.class);
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.addAll("X-Api-ProductId", List.of("MDS"));
        when(serverWebExchange.getRequest()).thenReturn(httpRequest);
        when(httpRequest.getURI()).thenReturn(URI.create("test1"));
        when(httpRequest.getHeaders()).thenReturn(httpHeaders);
    }

    @Test
    public void handleServerWebInputExceptionInvalidEnumValue() {
        String causeMessage = "JSON decoding error: Cannot deserialize value of type `com.morningstar.martgateway.domains.entitlement.entity.InvestmentApiIdType` from String \"MasterPortfolioIds\": not one of the values accepted for Enum class: [MasterPortfolioId, PerformanceId, SecId, StyleBoxRegionId, MorningstarIndustryCode]";

        ServerWebInputException serverWebInputException = mock(ServerWebInputException.class);
        DecodingException decodingException = mock(DecodingException.class);

        when(serverWebInputException.getCause()).thenReturn(decodingException);
        when(decodingException.getMessage()).thenReturn(causeMessage);

        ResponseEntity<InvestmentResponse> response = handler.handleServerWebInputException(
                serverWebInputException, serverWebExchange);
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertEquals(response.getBody().getStatus().getCode(), "400203");
        assertEquals(response.getBody().getStatus().getMessage(), "Bad Request: Invalid value [MasterPortfolioIds] for field [idType]. Valid values: [SecId, MasterPortfolioId]");
    }

    @Test
    public void handleServerWebInputExceptionEmptyEnumValue() {
        String causeMessage = "JSON decoding error: Cannot coerce empty String (\"\") to `com.morningstar.martgateway.domains.entitlement.entity.InvestmentApiIdType` value (but could if coercion was enabled using `CoercionConfig`)";

        ServerWebInputException serverWebInputException = mock(ServerWebInputException.class);
        DecodingException decodingException = mock(DecodingException.class);

        when(serverWebInputException.getCause()).thenReturn(decodingException);
        when(decodingException.getMessage()).thenReturn(causeMessage);

        when(serverWebExchange.getRequest().getURI()).thenReturn(URI.create("/portfolio-holdings-api/v1/data"));

        ResponseEntity<InvestmentResponse> response = handler.handleServerWebInputException(
                serverWebInputException, serverWebExchange);
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertEquals(response.getBody().getStatus().getCode(), "400203");
        assertEquals(response.getBody().getStatus().getMessage(), "Bad Request: Mandatory field `idType` is missing or empty");
    }

    @Test
    public void handleServerWebInputExceptionOtherDecodingException() {
        String causeMessage = "some other random error";

        ServerWebInputException serverWebInputException = mock(ServerWebInputException.class);
        DecodingException decodingException = mock(DecodingException.class);

        when(serverWebInputException.getCause()).thenReturn(decodingException);
        when(decodingException.getMessage()).thenReturn(causeMessage);

        ResponseEntity<InvestmentResponse> response = handler.handleServerWebInputException(
                serverWebInputException, serverWebExchange);
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertEquals(response.getBody().getStatus().getCode(), "400203");
        assertEquals(response.getBody().getStatus().getMessage(), "Invalid request input format");
    }

    @Test
    public void handleServerWebInputExceptionOtherException() {
        String initialMessage = "some other error";

        ServerWebInputException serverWebInputException = mock(ServerWebInputException.class);
        when(serverWebInputException.getMessage()).thenReturn(initialMessage);
        Exception otherException = mock(Exception.class);

        when(serverWebInputException.getCause()).thenReturn(otherException);

        ResponseEntity<InvestmentResponse> response = handler.handleServerWebInputException(
                serverWebInputException, serverWebExchange);
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertEquals(response.getBody().getStatus().getCode(), "400");
        assertEquals(response.getBody().getStatus().getMessage(), "some other error");
    }

    @Test
    public void handleHoldingException() {
        Status status = Status.BAD_REQUEST.withMessage("SomeValidationError");
        HoldingValidationException e = new HoldingValidationException(status);
        ResponseEntity<InvestmentResponse> response = handler.handleHoldingException(
                e, serverWebExchange);
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertEquals(response.getBody().getStatus().getCode(), "400");
        assertEquals(response.getBody().getStatus().getMessage(), "SomeValidationError");
    }

    @Test
    public void handleValidationException() {
        InvestmentApiValidationException e = new InvestmentApiValidationException(Status.MISSING_ATTRIBUTE);
        ResponseEntity<InvestmentResponse> response = handler.handleValidationException(
                e, serverWebExchange);
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertEquals(response.getBody().getStatus().getCode(), "400201");
        assertEquals(response.getBody().getStatus().getMessage(), "Request input missing mandatory attributes");
    }

    @Test
    public void handlePortfolioHoldingException() {
        PortfolioHoldingException e = new PortfolioHoldingException("401", "oopsie");
        ResponseEntity<InvestmentResponse> response = handler.handlePortfolioHoldingException(
                e, serverWebExchange);
        assertEquals(HttpStatus.UNAUTHORIZED, response.getStatusCode());
        assertEquals(response.getBody().getStatus().getCode(), "401");
        assertEquals(response.getBody().getStatus().getMessage(), "oopsie");
    }

    @Test
    public void handleEntitlementExceptionTest() {
        EntitlementException e = new EntitlementException(Status.REDIS_CONNECTION_FAILED, null);
        ResponseEntity<InvestmentResponse> response = handler.handleEntitlementException(
                e, serverWebExchange);
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertEquals(response.getBody().getStatus().getCode(), "500300");
        assertEquals(response.getBody().getStatus().getMessage(), "Redis connection failed");
    }

    @Test
    public void handleOtherException() {
        Exception e = new Exception("oh hi there");
        ResponseEntity<InvestmentResponse> response = handler.handleOtherException(
                e, serverWebExchange);
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertEquals(response.getBody().getStatus().getCode(), "500");
        assertEquals(response.getBody().getStatus().getMessage(), "Internal Server Error");
    }

    @Test
    public void testGetString_PortfolioDateType_HoldingData() {
        String url = "http://example.com/portfolio-holdings-api/v1/data";
        Matcher matcher = mock(Matcher.class);
        when(matcher.group(3)).thenReturn("mostRecent, timeSeries, staticDates");
        String result = invokeGetValidEnumValuesFromErrorMsg(url, matcher, "PortfolioDateType");
        assertEquals("mostRecent, staticDates", result);

        url = "http://example.com/portfolio-holdings-api/v1/stream-data";
        when(matcher.group(3)).thenReturn("mostRecent, timeSeries, staticDates");
        result = invokeGetValidEnumValuesFromErrorMsg(url, matcher, "PortfolioDateType");
        assertEquals("mostRecent, staticDates", result);
    }

    @Test
    public void testGetString_PortfolioDateType_HoldingDate() {
        String url = "http://example.com/portfolio-holdings-api/v1/dates";
        Matcher matcher = mock(Matcher.class);
        when(matcher.group(3)).thenReturn("mostRecent, timeSeries, staticDates");

        String result = invokeGetValidEnumValuesFromErrorMsg(url, matcher, "PortfolioDateType");

        assertEquals("mostRecent, timeSeries, staticDates", result);
    }

    @Test
    public void testGetString_PortfolioDateType_OtherEndpoint() {
        String url = "http://example.com/portfolio-holdings-api/v1/async-data";
        Matcher matcher = mock(Matcher.class);
        when(matcher.group(3)).thenReturn("mostRecent, timeSeries, staticDates");

        String result = invokeGetValidEnumValuesFromErrorMsg(url, matcher, "PortfolioDateType");

        assertEquals("mostRecent, timeSeries, staticDates", result);
    }

    @Test
    public void testGetString_InvestmentApiIdType() {
        String url = "http://example.com/holding-data";
        Matcher matcher = mock(Matcher.class);
        when(matcher.group(3)).thenReturn("MasterPortfolioId, PerformanceId, SecId, StyleBoxRegionId");

        String result = invokeGetValidEnumValuesFromErrorMsg(url, matcher, "InvestmentApiIdType");

        assertEquals("SecId, MasterPortfolioId", result);
    }

    @Test
    public void testGetString_DefaultCase() {
        String url = "http://example.com/holding-data";
        Matcher matcher = mock(Matcher.class);
        String originalValues = "value1, value2, value3";
        when(matcher.group(3)).thenReturn(originalValues);

        String result = invokeGetValidEnumValuesFromErrorMsg(url, matcher, "SomeOtherEnum");

        assertEquals(originalValues, result);
    }

    // Helper method to access private getString method
    private String invokeGetValidEnumValuesFromErrorMsg(String url, Matcher matcher, String enumName) {
        try {
            Method method = HoldingExceptionHandler.class.getDeclaredMethod("getValidEnumValuesFromErrorMsg",
                    String.class, Matcher.class, String.class);
            method.setAccessible(true);
            return (String) method.invoke(null, url, matcher, enumName);
        } catch (Exception e) {
            throw new RuntimeException("Failed to invoke getString method", e);
        }
    }
}