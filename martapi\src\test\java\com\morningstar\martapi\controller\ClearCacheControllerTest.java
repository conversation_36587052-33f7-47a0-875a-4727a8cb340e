package com.morningstar.martapi.controller;

import com.morningstar.martapi.service.RedisMessagePublisher;
import com.morningstar.martapi.exception.ValidationException;
import com.morningstar.martapi.validator.RequestValidationHandler;
import com.morningstar.martcommon.entity.ClearCacheRequest;
import com.morningstar.martgateway.domains.core.entity.response.MartResponse;
import com.morningstar.martgateway.domains.core.entity.response.Status;
import com.morningstar.martgateway.interfaces.MartGateway;
import java.util.UUID;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.ResponseEntity;
import reactor.core.publisher.Mono;

import java.util.Collections;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ClearCacheControllerTest {

    private ClearCacheController clearCacheController;
    private RedisMessagePublisher redisMessagePublisher;

    private MartGateway<MartResponse, ClearCacheRequest> clearCacheGateway;

    @Before
    public void setUp(){
        clearCacheGateway = mock(MartGateway.class);
        redisMessagePublisher = mock(RedisMessagePublisher.class);
        clearCacheController = new ClearCacheController(clearCacheGateway, new RequestValidationHandler<>(Collections.emptyList(), Collections.emptyList()), redisMessagePublisher);
    }

    @Test
    public void clearCacheTest() {
        MartResponse martResponse = new MartResponse();
        ClearCacheRequest clearCacheRequest = ClearCacheRequest.builder().build();
        Mono<MartResponse> martResponseMonoT = Mono.just(martResponse);
        when(clearCacheGateway.asyncRetrieveSecurities(any(ClearCacheRequest.class))).thenReturn(martResponseMonoT);
        Mono<MartResponse> martResponseMono = clearCacheController.clearCache(clearCacheRequest, "", "", "", null);
        martResponseMonoT.subscribe();
        martResponseMono.subscribe();
    }


    @Test
    public void clearUimTokenTest() {
        ResponseEntity<Void> response = clearCacheController.clearUimCache("MDS", UUID.randomUUID().toString(), "");
        assertEquals(204, response.getStatusCodeValue());

        ValidationException validationException = assertThrows(ValidationException.class, () -> clearCacheController.clearUimCache("oops", UUID.randomUUID().toString(), ""));
        assertEquals(Status.INVALID_PRODUCT_ID, validationException.getStatus());

        doThrow(RuntimeException.class).when(redisMessagePublisher).publishUimTokenClearCache(any());
        response = clearCacheController.clearUimCache("mds", UUID.randomUUID().toString(), "");
        assertEquals(500, response.getStatusCodeValue());
    }
}