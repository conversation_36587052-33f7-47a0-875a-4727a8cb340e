package com.morningstar.martapi.util;

import static org.junit.Assert.*;

import com.morningstar.dataac.martgateway.data.ph.entity.HoldingBufferSize;
import com.morningstar.dataac.martgateway.data.ph.entity.HoldingDataAsyncInput;
import com.morningstar.martapi.validator.portfolioholdings.HoldingDataRequestBuilder;
import com.morningstar.martgateway.interfaces.model.AsyncInput;
import com.morningstar.martgateway.interfaces.model.AsyncInput.ApiType;
import com.morningstar.martgateway.interfaces.model.investmentapi.GridViewAsyncInput;
import org.junit.Test;

public class BatchSizeUtilTest {

    @Test
    public void batchSizeForInvestmentApi() {
        AsyncInput asyncInput = GridViewAsyncInput.builder()
                .apiType(AsyncInput.ApiType.INVESTMENT_API)
                .build();
        BatchSizeUtil batchSizeUtil = new BatchSizeUtil(asyncInput);
        assertNull(batchSizeUtil.getBlBatchSize());
        assertNull(batchSizeUtil.getLtBatchSize());
    }

    @Test
    public void batchSizeForHoldingApiNoSizesPassed() {
        AsyncInput asyncInput = HoldingDataAsyncInput.builder()
                .apiType(ApiType.HOLDING_API)
                .holdingDataRequest(new HoldingDataRequestBuilder().build())
                .build();
        BatchSizeUtil batchSizeUtil = new BatchSizeUtil(asyncInput);
        assertEquals(HoldingBufferSize.getDefaultBlHoldingBufferSize(), batchSizeUtil.getBlBatchSize().intValue());
        assertEquals(HoldingBufferSize.getDefaultLtHoldingBufferSize(), batchSizeUtil.getLtBatchSize().intValue());
    }

    @Test
    public void batchSizeForHoldingApiWithSizes() {
        AsyncInput asyncInput = HoldingDataAsyncInput.builder()
                .apiType(ApiType.HOLDING_API)
                .holdingDataRequest(new HoldingDataRequestBuilder()
                        .blBatchSize(10)
                        .ltBatchSize(20)
                        .build())
                .build();
        BatchSizeUtil batchSizeUtil = new BatchSizeUtil(asyncInput);
        assertEquals(10, batchSizeUtil.getBlBatchSize().intValue());
        assertEquals(20, batchSizeUtil.getLtBatchSize().intValue());
    }

}