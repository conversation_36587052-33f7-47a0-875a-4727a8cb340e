package com.morningstar.martapi.controller;

import com.morningstar.martapi.entity.AsyncApiResponseEntity;
import com.morningstar.martapi.exception.InvestmentApiValidationException;
import com.morningstar.martapi.service.AsyncApiService;
import com.morningstar.martapi.util.TokenUtil;
import com.morningstar.martapi.validator.RequestValidationHandler;
import com.morningstar.martapi.validator.entity.HeadersAndParams;
import com.morningstar.martgateway.domains.core.entity.response.Status;
import com.morningstar.martgateway.interfaces.model.AsyncInput;
import com.morningstar.martgateway.interfaces.model.investmentapi.GridViewAsyncInput;
import com.morningstar.martgateway.interfaces.model.investmentapi.InvestmentApiRequest;
import javax.inject.Inject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Scheduler;

import java.util.List;

@RestController
@RequestMapping(value = {"/v1","/investment-api/v1"})
public class AsyncApiController {

    private final AsyncApiService asyncApiService;
    private final RequestValidationHandler<HeadersAndParams,InvestmentApiRequest> validator;
    private final Scheduler asyncScheduler;

    private static final Logger log = LoggerFactory.getLogger(AsyncApiController.class);

    @Inject
    public AsyncApiController(
            AsyncApiService asyncApiService,
            @Qualifier("asyncApiValidator") RequestValidationHandler<HeadersAndParams,InvestmentApiRequest> validator,
            @Qualifier("asyncScheduler") Scheduler asyncScheduler
    ) {
        this.asyncApiService = asyncApiService;
        this.validator = validator;
        this.asyncScheduler = asyncScheduler;
    }

    @PostMapping(value="/async-data")
    public Mono<AsyncApiResponseEntity> fetchAsyncData(
            @RequestHeader(value = "X-Api-ProductId", required = false, defaultValue = "") String productId,
            @RequestHeader(value = "X-Api-RequestId", required = false, defaultValue = "") String requestId,
            @RequestHeader(value = "Authorization", required = false, defaultValue = "") String token,
            @RequestBody InvestmentApiRequest investmentApiRequest
    ) {
        HeadersAndParams headersAndParams = HeadersAndParams.builder()
                .authorizationToken(token)
                .productId(productId)
                .requestId(requestId)
                .build();

        String userIdFromToken = TokenUtil.getUserId(token);
        String configId = TokenUtil.getConfigId(token);

        AsyncInput asyncInput = GridViewAsyncInput.builder()
                .productId(productId)
                .userId(userIdFromToken)
                .configId(configId)
                .investmentApiRequest(investmentApiRequest)
                .apiType(AsyncInput.ApiType.INVESTMENT_API)
                .build();

        validator.validateHeadersAndParams(headersAndParams);
        validator.validateRequestBody(investmentApiRequest);
        validator.validateDataEntitlement(userIdFromToken, configId);
        return Mono.fromCallable(() -> asyncApiService.fetchAsyncData(asyncInput))
                .subscribeOn(asyncScheduler)
                .doOnError(e -> {
                    log.info("event_type=\"Async Api error\", event_description=\"Async Api fetch Async Data failed\", request_id=\"{}\", error_message=\"{}\"", requestId, e.getMessage());
                    if (e instanceof InvestmentApiValidationException e1) {
                        throw e1;
                    } else
                        throw new InvestmentApiValidationException(Status.INTERNAL_ERROR);
                });

    }

    @GetMapping(value="/async-status/{jobId}")
    public Mono<AsyncApiResponseEntity> getAsyncJobStatus(
            @RequestHeader(value = "X-Api-ProductId", required = false, defaultValue = "") String productId,
            @RequestHeader(value = "X-Api-RequestId", required = false, defaultValue = "") String requestId,
            @RequestHeader(value = "X-Api-UserId", required = false, defaultValue = "") String userId,
            @RequestHeader(value = "Authorization", required = false, defaultValue = "") String bearerToken,
            @PathVariable("jobId") String jobId
    ){
        HeadersAndParams headersAndParams = HeadersAndParams.builder()
                .authorizationToken(bearerToken)
                .productId(productId)
                .requestId(requestId)
                .build();
        validator.validateHeadersAndParams(headersAndParams);
        return  Mono.fromCallable(() -> asyncApiService.getGridviewStatus(productId, bearerToken, jobId.toLowerCase(), userId))
                .subscribeOn(asyncScheduler)
                .doOnError(e -> {
                    if (e instanceof InvestmentApiValidationException e1)
                        throw e1;
                    else
                        throw new InvestmentApiValidationException(Status.INTERNAL_ERROR);
                });
    }
}
