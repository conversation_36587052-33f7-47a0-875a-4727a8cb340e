package com.morningstar.martapi.validator.clearcache;

import com.morningstar.martapi.exception.ValidationException;
import com.morningstar.martcommon.entity.ClearCacheRequest;
import com.morningstar.martgateway.domains.core.entity.response.Status;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;

import java.util.List;

public class InvestmentValidatorTest {

    private com.morningstar.martapi.validator.clearcache.InvestmentValidator validator;

    @Before
    public void setup() {
        this.validator = new InvestmentValidator();
    }

    @Test
    public void validateInvestment() {
        ClearCacheRequest request = ClearCacheRequest.builder()
                .investmentIds(List.of(""))
                .investmentIds(List.of("id1")).build();
        validator.validate(request);
    }

    @Test
    public void validateMissingInvestment() {
        ClearCacheRequest request = ClearCacheRequest.builder()
                .investmentIds(List.of(""))
                .dataPoints(List.of("HP010","HP022")).build();
        ValidationException exception = Assertions.assertThrows(ValidationException.class,
                () -> validator.validate(request));
        Assertions.assertEquals(Status.MISSING_ATTRIBUTE.getCode(),exception.getStatus().getCode());
    }
}