package com.morningstar.martapi.validator.investmentapi;

import com.morningstar.martapi.exception.InvestmentApiValidationException;
import com.morningstar.martgateway.domains.core.entity.response.Status;
import com.morningstar.martcommon.entity.payload.gridview.GridviewDataPoint;
import com.morningstar.martgateway.interfaces.model.investmentapi.Investment;
import com.morningstar.martgateway.interfaces.model.investmentapi.InvestmentApiRequest;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;

import java.util.List;

public class DateValidatorTest {

    private DateValidator validator;

    @Before
    public void setup() {
        this.validator = new DateValidator();
    }

    @Test
    public void endDateBeforeStartDate() {
        InvestmentApiRequest request = InvestmentApiRequest.builder()
                .useCase("feed")
                .investments(List.of(Investment.builder().id("OP01010").build(),Investment.builder().id("OP01012").build()))
                .dataPoints(List.of(
                        GridviewDataPoint.builder().dataPointId("HP022").build(),
                        GridviewDataPoint.builder().dataPointId("HP010").startDate("2020-12-31").endDate("2021-01-01").build(),
                        GridviewDataPoint.builder().dataPointId("HP010").startDate("2022-12-31").endDate("2021-01-01").build()
                ))
                .build();
        InvestmentApiValidationException exception = Assertions.assertThrows(InvestmentApiValidationException.class,
                () -> validator.validate(request));
        Assertions.assertEquals(Status.INVALID_DATE_SETTING.getCode(), exception.getStatus().getCode());
    }

    @Test
    public void missingStartDate() {
        InvestmentApiRequest request = InvestmentApiRequest.builder()
                .useCase("feed")
                .investments(List.of(Investment.builder().id("OP01010").build(),Investment.builder().id("OP01012").build()))
                .dataPoints(List.of(
                        GridviewDataPoint.builder().dataPointId("HP010").startDate("2020-12-31").endDate("2021-01-01").build(),
                        GridviewDataPoint.builder().dataPointId("HP010").endDate("2021-01-01").build(),
                        GridviewDataPoint.builder().dataPointId("HP022").build()
                ))
                .build();
        InvestmentApiValidationException exception = Assertions.assertThrows(InvestmentApiValidationException.class,
                () -> validator.validate(request));
        Assertions.assertEquals(Status.INVALID_DATE_SETTING.getCode(), exception.getStatus().getCode());
    }

    @Test
    public void missingEndDate() {
        InvestmentApiRequest request = InvestmentApiRequest.builder()
                .useCase("feed")
                .investments(List.of(Investment.builder().id("OP01010").build(),Investment.builder().id("OP01012").build()))
                .dataPoints(List.of(
                        GridviewDataPoint.builder().dataPointId("HP010").startDate("2020-12-31").endDate("2021-01-01").build(),
                        GridviewDataPoint.builder().dataPointId("HP010").startDate("2021-01-01").build(),
                        GridviewDataPoint.builder().dataPointId("HP022").build()
                ))
                .build();
        InvestmentApiValidationException exception = Assertions.assertThrows(InvestmentApiValidationException.class,
                () -> validator.validate(request));
        Assertions.assertEquals(Status.INVALID_DATE_SETTING.getCode(), exception.getStatus().getCode());
    }

    @Test
    public void invalidDateFormat() {
        InvestmentApiRequest request = InvestmentApiRequest.builder()
                .useCase("feed")
                .investments(List.of(Investment.builder().id("OP01010").build(),Investment.builder().id("OP01012").build()))
                .dataPoints(List.of(
                        GridviewDataPoint.builder().dataPointId("HP010").startDate("2020-12-31").endDate("2021-01-01").build(),
                        GridviewDataPoint.builder().dataPointId("HP010").startDate("25/12/2020").endDate("01/01/2023").build(),
                        GridviewDataPoint.builder().dataPointId("HP022").build()
                ))
                .build();
        InvestmentApiValidationException exception = Assertions.assertThrows(InvestmentApiValidationException.class,
                () -> validator.validate(request));
        Assertions.assertEquals(Status.INVALID_DATE_SETTING.getCode(), exception.getStatus().getCode());
    }

    @Test
    public void validate() {
        InvestmentApiRequest request = InvestmentApiRequest.builder()
                .useCase("feed")
                .investments(List.of(Investment.builder().id("OP01010").build(),Investment.builder().id("OP01012").build()))
                .dataPoints(List.of(
                        GridviewDataPoint.builder().dataPointId("HP010").startDate("2020-12-31").endDate("2021-01-01").build(),
                        GridviewDataPoint.builder().dataPointId("HP022").build()
                ))
                .build();
        Assertions.assertDoesNotThrow(() -> validator.validate(request));
    }
    @Test
    public void testInvalidDate() {
        InvestmentApiRequest request = InvestmentApiRequest.builder()
                .investments(List.of(Investment.builder().id("0P0000003H").build(),Investment.builder().id("0P00000032").build()))
                .dataPoints(List.of(
                        GridviewDataPoint.builder().dataPointId("EQ010").startDate("2020-12-31").endDate("2021-02-31").build(),
                        GridviewDataPoint.builder().dataPointId("EQ022").build()
                ))
                .build();
        InvestmentApiValidationException exception = Assertions.assertThrows(InvestmentApiValidationException.class,
                () -> validator.validate(request));
        Assertions.assertEquals(Status.INVALID_DATE_SETTING.getCode(), exception.getStatus().getCode());
    }
}
