package com.morningstar.martapi.exception;

import static org.mockito.Mockito.when;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpHeaders;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.server.ServerWebExchange;

import java.net.URI;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class WebExceptionHandlerTest {
    @Mock
    private ServerHttpRequest httpRequest;
    @Mock
    private ServerWebExchange serverWebExchange;
    private URI uri;
    private WebExceptionHandler webExceptionHandler;

    @Before
    public void setUp(){
        uri = URI.create("test1");
        String s = uri.toString();
    }

    @Test
    public void shouldCustomizeTest() {
        List<String> idList = Arrays.asList("F0GBR04E6W");;
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.addAll("X-Api-ProductId",idList);
        when(serverWebExchange.getRequest()).thenReturn(httpRequest);
        when(httpRequest.getURI()).thenReturn(uri);
        when(httpRequest.getHeaders()).thenReturn(httpHeaders);
        webExceptionHandler = new WebExceptionHandler();
        webExceptionHandler.handleBadRequest(new Exception(),serverWebExchange);
    }
    @Test
    public void shouldCustomizeNullTest() {
        List<String> idList = new ArrayList<>();;
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.addAll("X-Api-ProductId",idList);
        when(serverWebExchange.getRequest()).thenReturn(httpRequest);
        when(httpRequest.getURI()).thenReturn(uri);
        when(httpRequest.getHeaders()).thenReturn(httpHeaders);
        webExceptionHandler = new WebExceptionHandler();
        webExceptionHandler.handleBadRequest(new Exception(),serverWebExchange);
    }
    @Test
    public void shouldHandleFailedDataAccessTest() {
        List<String> idList = Arrays.asList("F0GBR04E6W");;
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.addAll("X-Api-ProductId",idList);
        when(serverWebExchange.getRequest()).thenReturn(httpRequest);
        when(httpRequest.getURI()).thenReturn(uri);
        when(httpRequest.getHeaders()).thenReturn(httpHeaders);
        webExceptionHandler = new WebExceptionHandler();
        webExceptionHandler.handleFailedDataAccess(new Exception(),serverWebExchange);
    }
    @Test
    public void shouldHandleFailedDataAccessNullTest() {
        List<String> idList = new ArrayList<>();;
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.addAll("X-Api-ProductId",idList);
        when(serverWebExchange.getRequest()).thenReturn(httpRequest);
        when(httpRequest.getURI()).thenReturn(uri);
        when(httpRequest.getHeaders()).thenReturn(httpHeaders);
        webExceptionHandler = new WebExceptionHandler();
        webExceptionHandler.handleFailedDataAccess(new Exception(),serverWebExchange);
    }
    @Test
    public void shouldHandleUnExceptedExceptionsTest() {
        List<String> idList = Arrays.asList("F0GBR04E6W");;
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.addAll("X-Api-ProductId",idList);
        when(serverWebExchange.getRequest()).thenReturn(httpRequest);
        when(httpRequest.getURI()).thenReturn(uri);
        when(httpRequest.getHeaders()).thenReturn(httpHeaders);
        webExceptionHandler = new WebExceptionHandler();
        webExceptionHandler.handleUnExceptedExceptions(new Exception(),serverWebExchange);
    }
    @Test
    public void shouldHandleUnExceptedExceptionsNullTest() {
        List<String> idList = new ArrayList<>();;
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.addAll("X-Api-ProductId",idList);
        when(serverWebExchange.getRequest()).thenReturn(httpRequest);
        when(httpRequest.getURI()).thenReturn(uri);
        when(httpRequest.getHeaders()).thenReturn(httpHeaders);
        webExceptionHandler = new WebExceptionHandler();
        webExceptionHandler.handleUnExceptedExceptions(new Exception(),serverWebExchange);
    }
}
