package com.morningstar.martapi;

import com.morningstar.martgateway.util.ExcludeFromTests;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;
import org.springframework.scheduling.annotation.EnableScheduling;
@ExcludeFromTests
@SpringBootApplication(exclude = {RedisAutoConfiguration.class, MongoAutoConfiguration.class})
@ComponentScan(
        {
                "com.morningstar.dataac.martgateway",
                "com.morningstar.martgateway",
                "com.morningstar.martapi"
        })
@MapperScan("com.morningstar.martgateway.infrastructures.repo.data")
@EnableScheduling
@Import(com.morningstar.entitlement.eod.data.configuration.EntitlementEODAutoConfigure.class)
public class MartAPIApplication {
    public static void main(String[] args) {
        //enable reactor debug mode
        //remove buffer setting
        SpringApplication.run(MartAPIApplication.class, args);
    }
}

